//
//

package xtable;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import mkdb.TTable;
import xbean.LianyunAwardInfo;
import xbean.Pod;
import com.locojoy.base.Octets;

class _Tables_$109 extends TTable<Integer, LianyunAwardInfo> {
    _Tables_$109(_Tables_ this$0) {
        this.this$0 = this$0;
    }

    public String getName() {
        return "lianyunaward";
    }

    public OctetsStream marshalKey(Integer key) {
        OctetsStream _os_ = new OctetsStream();
        _os_.marshal(key);
        return _os_;
    }

    public OctetsStream marshalValue(LianyunAwardInfo value) {
        OctetsStream _os_ = new OctetsStream();
        value.marshal(_os_);
        return _os_;
    }

    public Integer unmarshalKey(OctetsStream _os_) throws MarshalException {
        int key = 0;
        key = _os_.unmarshal_int();
        return key;
    }

    public LianyunAwardInfo unmarshalValue(OctetsStream _os_) throws MarshalException {
        LianyunAwardInfo value = Pod.newLianyunAwardInfo();
        value.unmarshal(_os_);
        return value;
    }

    public LianyunAwardInfo newValue() {
        LianyunAwardInfo value = Pod.newLianyunAwardInfo();
        return value;
    }
}
