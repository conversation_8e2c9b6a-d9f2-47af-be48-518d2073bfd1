//
//

package xtable;

import mkdb.TField;
import mkdb.TTable;
import mkdb.TTableCache;
import xbean.PvP5Role;

public class Pvp5roles {
    Pvp5roles() {
    }

    public static PvP5Role get(Long key) {
        return (PvP5Role)_Tables_.getInstance().pvp5roles.get(key);
    }

    public static PvP5Role get(Long key, PvP5Role value) {
        return (PvP5Role)_Tables_.getInstance().pvp5roles.get(key, value);
    }

    public static void insert(Long key, PvP5Role value) {
        _Tables_.getInstance().pvp5roles.insert(key, value);
    }

    public static void delete(Long key) {
        _Tables_.getInstance().pvp5roles.delete(key);
    }

    public static boolean add(Long key, PvP5Role value) {
        return _Tables_.getInstance().pvp5roles.add(key, value);
    }

    public static boolean remove(Long key) {
        return _Tables_.getInstance().pvp5roles.remove(key);
    }

    public static TTableCache<Long, PvP5Role> getCache() {
        return _Tables_.getInstance().pvp5roles.getCache();
    }

    public static TTable<Long, PvP5Role> getTable() {
        return _Tables_.getInstance().pvp5roles;
    }

    public static PvP5Role select(Long key) {
        return (PvP5Role)getTable().select(key, new TField<PvP5Role, PvP5Role>() {
            public PvP5Role get(PvP5Role v) {
                return v.toData();
            }
        });
    }

    public static Integer selectGrade(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getGrade();
            }
        });
    }

    public static Integer selectCamp(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getCamp();
            }
        });
    }

    public static Integer selectExcellent(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getExcellent();
            }
        });
    }

    public static Integer selectScore(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getScore();
            }
        });
    }

    public static Integer selectBattlenum(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getBattlenum();
            }
        });
    }

    public static Integer selectWinnum(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getWinnum();
            }
        });
    }

    public static Integer selectContinuewinnum(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getContinuewinnum();
            }
        });
    }

    public static Integer selectFirstwinaward(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getFirstwinaward();
            }
        });
    }

    public static Integer selectFivebattleaward(Long key) {
        return (Integer)getTable().select(key, new TField<PvP5Role, Integer>() {
            public Integer get(PvP5Role v) {
                return v.getFivebattleaward();
            }
        });
    }

    public static Long selectLastbattletime(Long key) {
        return (Long)getTable().select(key, new TField<PvP5Role, Long>() {
            public Long get(PvP5Role v) {
                return v.getLastbattletime();
            }
        });
    }

    public static Long selectLastentertime(Long key) {
        return (Long)getTable().select(key, new TField<PvP5Role, Long>() {
            public Long get(PvP5Role v) {
                return v.getLastentertime();
            }
        });
    }
}
