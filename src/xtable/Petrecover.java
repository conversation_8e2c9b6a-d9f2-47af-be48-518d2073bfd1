//
//

package xtable;

import java.util.List;
import mkdb.TField;
import mkdb.TTable;
import mkdb.TTableCache;
import xbean.Petrecoverlist;

public class Petrecover {
    Petrecover() {
    }

    public static Petrecoverlist get(Long key) {
        return (Petrecoverlist)_Tables_.getInstance().petrecover.get(key);
    }

    public static Petrecoverlist get(Long key, Petrecoverlist value) {
        return (Petrecoverlist)_Tables_.getInstance().petrecover.get(key, value);
    }

    public static void insert(Long key, Petrecoverlist value) {
        _Tables_.getInstance().petrecover.insert(key, value);
    }

    public static void delete(Long key) {
        _Tables_.getInstance().petrecover.delete(key);
    }

    public static boolean add(Long key, Petrecoverlist value) {
        return _Tables_.getInstance().petrecover.add(key, value);
    }

    public static boolean remove(Long key) {
        return _Tables_.getInstance().petrecover.remove(key);
    }

    public static TTableCache<Long, Petrecoverlist> getCache() {
        return _Tables_.getInstance().petrecover.getCache();
    }

    public static TTable<Long, Petrecoverlist> getTable() {
        return _Tables_.getInstance().petrecover;
    }

    public static Petrecoverlist select(Long key) {
        return (Petrecoverlist)getTable().select(key, new TField<Petrecoverlist, Petrecoverlist>() {
            public Petrecoverlist get(Petrecoverlist v) {
                return v.toData();
            }
        });
    }

    public static List<Long> selectUniqids(Long key) {
        return (List)getTable().select(key, new TField<Petrecoverlist, List<Long>>() {
            public List<Long> get(Petrecoverlist v) {
                return v.getUniqidsAsData();
            }
        });
    }
}
