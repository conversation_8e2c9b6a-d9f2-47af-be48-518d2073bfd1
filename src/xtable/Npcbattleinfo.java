//
//

package xtable;

import java.util.Map;
import mkdb.TField;
import mkdb.TTable;
import mkdb.TTableCache;
import xbean.npcBattleInfoCol;

public class Npcbattleinfo {
    Npcbattleinfo() {
    }

    public static npcBattleInfoCol get(Long key) {
        return (npcBattleInfoCol)_Tables_.getInstance().npcbattleinfo.get(key);
    }

    public static npcBattleInfoCol get(Long key, npcBattleInfoCol value) {
        return (npcBattleInfoCol)_Tables_.getInstance().npcbattleinfo.get(key, value);
    }

    public static void insert(Long key, npcBattleInfoCol value) {
        _Tables_.getInstance().npcbattleinfo.insert(key, value);
    }

    public static void delete(Long key) {
        _Tables_.getInstance().npcbattleinfo.delete(key);
    }

    public static boolean add(Long key, npcBattleInfoCol value) {
        return _Tables_.getInstance().npcbattleinfo.add(key, value);
    }

    public static boolean remove(Long key) {
        return _Tables_.getInstance().npcbattleinfo.remove(key);
    }

    public static TTableCache<Long, npcBattleInfoCol> getCache() {
        return _Tables_.getInstance().npcbattleinfo.getCache();
    }

    public static TTable<Long, npcBattleInfoCol> getTable() {
        return _Tables_.getInstance().npcbattleinfo;
    }

    public static npcBattleInfoCol select(Long key) {
        return (npcBattleInfoCol)getTable().select(key, new TField<npcBattleInfoCol, npcBattleInfoCol>() {
            public npcBattleInfoCol get(npcBattleInfoCol v) {
                return v.toData();
            }
        });
    }

    public static Map<Long, Integer> selectBattleroles(Long key) {
        return (Map)getTable().select(key, new TField<npcBattleInfoCol, Map<Long, Integer>>() {
            public Map<Long, Integer> get(npcBattleInfoCol v) {
                return v.getBattlerolesAsData();
            }
        });
    }
}
