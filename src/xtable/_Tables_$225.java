//
//

package xtable;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import mkdb.TTable;
import xbean.Pod;
import xbean.ServiceSet;
import com.locojoy.base.Octets;
import java.util.Set;

class _Tables_$225 extends TTable<Integer, ServiceSet> {
    _Tables_$225(_Tables_ this$0) {
        this.this$0 = this$0;
    }

    public String getName() {
        return "servicesets";
    }

    public OctetsStream marshalKey(Integer key) {
        OctetsStream _os_ = new OctetsStream();
        _os_.marshal(key);
        return _os_;
    }

    public OctetsStream marshalValue(ServiceSet value) {
        OctetsStream _os_ = new OctetsStream();
        value.marshal(_os_);
        return _os_;
    }

    public Integer unmarshalKey(OctetsStream _os_) throws MarshalException {
        int key = 0;
        key = _os_.unmarshal_int();
        return key;
    }

    public ServiceSet unmarshalValue(OctetsStream _os_) throws MarshalException {
        ServiceSet value = Pod.newServiceSet();
        value.unmarshal(_os_);
        return value;
    }

    public ServiceSet newValue() {
        ServiceSet value = Pod.newServiceSet();
        return value;
    }
}
