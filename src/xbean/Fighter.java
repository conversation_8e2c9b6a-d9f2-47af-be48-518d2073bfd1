//
//

package xbean;

import com.locojoy.base.Octets;
import java.util.Map;
import mkdb.Bean;

public interface Fighter extends Bean {
    int DrugStuffID = 275;
    int DrugStuffID_LV2 = 323;
    int FoodID = 290;
    int DrugID = 291;
    int DrugStuffTime = 20;
    int DrugFoodTime = 10;
    int FIGHTER_ROLE = 1;
    int FIGHTER_PET = 2;
    int FIGHTER_PARTNER = 3;
    int FIGHTER_MONSTER_HIDE = 4;
    int FIGHTER_MONSTER_NPC = 5;
    int FIGHTER_SYSTEM_PARTNER = 6;
    int PHY_ATTACK = 1;
    int PHY_HURT = 2;
    int MAGIC_ATTACK = 3;
    int MAGIC_HURT = 4;

    Fighter copy();

    Fighter toData();

    Fighter toBean();

    Fighter toDataIf();

    Fighter toBeanIf();

    long getUniqueid();

    String getFightername();

    Octets getFighternameOctets();

    int getSubtype();

    int getFightertype();

    int getBattleindex();

    int getFighterkey();

    boolean getIshost();

    int getRound();

    int getPositionx();

    int getPositiony();

    int getInilevel();

    int getInihp();

    int getInimp();

    int getInisp();

    int getInipetkey();

    int getFootlogoid();

    int getShapeid();

    Map<Integer, Integer> getActioncount();

    Map<Integer, Integer> getActioncountAsData();

    Map<Integer, Integer> getFightedpets();

    Map<Integer, Integer> getFightedpetsAsData();

    Map<Integer, Float> getInitattrs();

    Map<Integer, Float> getInitattrsAsData();

    Map<Integer, Integer> getInitskills();

    Map<Integer, Integer> getInitskillsAsData();

    Map<Integer, Integer> getUseditems();

    Map<Integer, Integer> getUseditemsAsData();

    void setUniqueid(long value);

    void setFightername(String value);

    void setFighternameOctets(Octets value);

    void setSubtype(int value);

    void setFightertype(int value);

    void setBattleindex(int value);

    void setFighterkey(int value);

    void setIshost(boolean value);

    void setRound(int value);

    void setPositionx(int value);

    void setPositiony(int value);

    void setInilevel(int value);

    void setInihp(int value);

    void setInimp(int value);

    void setInisp(int value);

    void setInipetkey(int value);

    void setFootlogoid(int value);

    void setShapeid(int value);
}
