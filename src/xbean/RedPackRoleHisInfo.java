//
//

package xbean;

import mkdb.Bean;

public interface RedPackRoleHisInfo extends Bean {
    RedPackRoleHisInfo copy();

    RedPackRoleHisInfo toData();

    RedPackRoleHisInfo toBean();

    RedPackRoleHisInfo toDataIf();

    RedPackRoleHisInfo toBeanIf();

    long getRoleid();

    int getRedpackmoney();

    long getReceivetime();

    void setRoleid(long value);

    void setRedpackmoney(int value);

    void setReceivetime(long value);
}
