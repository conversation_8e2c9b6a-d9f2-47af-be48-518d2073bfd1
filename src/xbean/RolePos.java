//
//

package xbean;

import mkdb.Bean;
import java.util.Map;

public interface RolePos extends Bean {
    int OUTDREAM = 1;
    int INDREAM = 2;
    int ABSENTDREAM = 3;

    RolePos copy();

    RolePos toData();

    RolePos toBean();

    RolePos toDataIf();

    RolePos toBeanIf();

    int getMapid();

    int getPosx();

    int getPosy();

    long getOwnerid();

    int getDynamicmap();

    int getDynamicposx();

    int getDynamicposy();

    int getStatus();

    int getHastask();

    void setMapid(int value);

    void setPosx(int value);

    void setPosy(int value);

    void setOwnerid(long value);

    void setDynamicmap(int value);

    void setDynamicposx(int value);

    void setDynamicposy(int value);

    void setStatus(int value);

    void setHastask(int value);
}
