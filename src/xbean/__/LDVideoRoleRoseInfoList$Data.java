//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import java.util.HashMap;
import java.util.Map;
import mkdb.Bean;
import mkdb.XBean;
import xbean.LDVideoRoleRoseInfo;
import xbean.LDVideoRoleRoseInfoList;
import xbean.Pod;
import com.locojoy.base.Octets;
import java.util.List;
import java.util.Set;

public final class LDVideoRoleRoseInfoList$Data implements LDVideoRoleRoseInfoList {
    private HashMap<String, LDVideoRoleRoseInfo> ldvideoroleroseinfolist;

    public void _reset_unsafe_() {
        throw new UnsupportedOperationException();
    }

    public LDVideoRoleRoseInfoList$Data() {
        this.ldvideoroleroseinfolist = new HashMap();
    }

    LDVideoRoleRoseInfoList$Data(LDVideoRoleRoseInfoList _o1_) {
        if (_o1_ instanceof xbean.__.LDVideoRoleRoseInfoList) {
            this.assign((xbean.__.LDVideoRoleRoseInfoList)_o1_);
        } else if (_o1_ instanceof LDVideoRoleRoseInfoList$Data) {
            this.assign((LDVideoRoleRoseInfoList$Data)_o1_);
        } else {
            if (!(_o1_ instanceof xbean.__.LDVideoRoleRoseInfoList.Const)) {
                throw new UnsupportedOperationException();
            }

            this.assign(((xbean.__.LDVideoRoleRoseInfoList.Const)_o1_).nThis());
        }

    }

    private void assign(xbean.__.LDVideoRoleRoseInfoList _o_) {
        this.ldvideoroleroseinfolist = new HashMap();

        for(Map.Entry<String, LDVideoRoleRoseInfo> _e_ : xbean.__.LDVideoRoleRoseInfoList.access$200(_o_).entrySet()) {
            this.ldvideoroleroseinfolist.put(_e_.getKey(), new xbean.__.LDVideoRoleRoseInfo.Data((LDVideoRoleRoseInfo)_e_.getValue()));
        }

    }

    private void assign(LDVideoRoleRoseInfoList$Data _o_) {
        this.ldvideoroleroseinfolist = new HashMap();

        for(Map.Entry<String, LDVideoRoleRoseInfo> _e_ : _o_.ldvideoroleroseinfolist.entrySet()) {
            this.ldvideoroleroseinfolist.put(_e_.getKey(), new xbean.__.LDVideoRoleRoseInfo.Data((LDVideoRoleRoseInfo)_e_.getValue()));
        }

    }

    public final OctetsStream marshal(OctetsStream _os_) {
        _os_.compact_uint32(this.ldvideoroleroseinfolist.size());

        for(Map.Entry<String, LDVideoRoleRoseInfo> _e_ : this.ldvideoroleroseinfolist.entrySet()) {
            _os_.marshal((String)_e_.getKey(), "UTF-16LE");
            ((LDVideoRoleRoseInfo)_e_.getValue()).marshal(_os_);
        }

        return _os_;
    }

    public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        int size = _os_.uncompact_uint32();
        if (size >= 12) {
            this.ldvideoroleroseinfolist = new HashMap(size * 2);
        }

        while(size > 0) {
            String _k_ = "";
            _k_ = _os_.unmarshal_String("UTF-16LE");
            LDVideoRoleRoseInfo _v_ = Pod.newLDVideoRoleRoseInfoData();
            _v_.unmarshal(_os_);
            this.ldvideoroleroseinfolist.put(_k_, _v_);
            --size;
        }

        return _os_;
    }

    public LDVideoRoleRoseInfoList copy() {
        return new LDVideoRoleRoseInfoList$Data(this);
    }

    public LDVideoRoleRoseInfoList toData() {
        return new LDVideoRoleRoseInfoList$Data(this);
    }

    public LDVideoRoleRoseInfoList toBean() {
        return new xbean.__.LDVideoRoleRoseInfoList(this, (XBean)null, (String)null);
    }

    public LDVideoRoleRoseInfoList toDataIf() {
        return this;
    }

    public LDVideoRoleRoseInfoList toBeanIf() {
        return new xbean.__.LDVideoRoleRoseInfoList(this, (XBean)null, (String)null);
    }

    public boolean xdbManaged() {
        throw new UnsupportedOperationException();
    }

    public Bean xdbParent() {
        throw new UnsupportedOperationException();
    }

    public String xdbVarname() {
        throw new UnsupportedOperationException();
    }

    public Long xdbObjId() {
        throw new UnsupportedOperationException();
    }

    public Bean toConst() {
        throw new UnsupportedOperationException();
    }

    public boolean isConst() {
        return false;
    }

    public boolean isData() {
        return true;
    }

    public Map<String, LDVideoRoleRoseInfo> getLdvideoroleroseinfolist() {
        return this.ldvideoroleroseinfolist;
    }

    public Map<String, LDVideoRoleRoseInfo> getLdvideoroleroseinfolistAsData() {
        return this.ldvideoroleroseinfolist;
    }

    public final boolean equals(Object _o1_) {
        if (!(_o1_ instanceof LDVideoRoleRoseInfoList$Data)) {
            return false;
        } else {
            LDVideoRoleRoseInfoList$Data _o_ = (LDVideoRoleRoseInfoList$Data)_o1_;
            return this.ldvideoroleroseinfolist.equals(_o_.ldvideoroleroseinfolist);
        }
    }

    public final int hashCode() {
        int _h_ = 0;
        _h_ += this.ldvideoroleroseinfolist.hashCode();
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.ldvideoroleroseinfolist);
        _sb_.append(")");
        return _sb_.toString();
    }
}
