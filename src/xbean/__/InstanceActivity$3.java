//
//

package xbean.__;

import mkdb.Log;
import mkdb.LogKey;
import mkdb.XBean;
import mkdb.Bean;

class InstanceActivity$3 extends LogKey {
    InstanceActivity$3(InstanceActivity this$0, XBean arg0, String arg1) {
        super(arg0, arg1);
        this.this$0 = this$0;
    }

    protected Log create() {
        return new InstanceActivity$3$1(this, this, InstanceActivity.access$800(this.this$0));
    }
}
