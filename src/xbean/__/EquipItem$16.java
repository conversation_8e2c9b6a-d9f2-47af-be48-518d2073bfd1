//
//

package xbean.__;

import mkdb.Log;
import mkdb.LogKey;
import mkdb.XBean;
import mkdb.Bean;

class EquipItem$16 extends LogKey {
    EquipItem$16(EquipItem var1, XBean $anonymous0, String $anonymous1) {
        super($anonymous0, $anonymous1);
        this.this$0 = var1;
    }

    protected Log create() {
        return new EquipItem$16$1(this, this, EquipItem.access$15(this.this$0));
    }
}
