//
//

package xbean.__;

import mkdb.Log;
import mkdb.LogKey;
import mkdb.XBean;
import mkdb.Bean;

class ActiveUserInfo$27 extends LogKey {
    ActiveUserInfo$27(ActiveUserInfo this$0, XBean arg0, String arg1) {
        super(arg0, arg1);
        this.this$0 = this$0;
    }

    protected Log create() {
        return new ActiveUserInfo$27$1(this, this, ActiveUserInfo.access$6400(this.this$0));
    }
}
