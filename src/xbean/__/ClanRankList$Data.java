//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import java.util.LinkedList;
import java.util.List;
import mkdb.Bean;
import mkdb.XBean;
import xbean.ClanRankList;
import xbean.ClanRankRecord;
import xbean.Pod;
import com.locojoy.base.Octets;

public final class ClanRankList$Data implements ClanRankList {
    private LinkedList<ClanRankRecord> records;

    public void _reset_unsafe_() {
        throw new UnsupportedOperationException();
    }

    public ClanRankList$Data() {
        this.records = new LinkedList();
    }

    ClanRankList$Data(ClanRankList _o1_) {
        if (_o1_ instanceof xbean.__.ClanRankList) {
            this.assign((xbean.__.ClanRankList)_o1_);
        } else if (_o1_ instanceof ClanRankList$Data) {
            this.assign((ClanRankList$Data)_o1_);
        } else {
            if (!(_o1_ instanceof xbean.__.ClanRankList.Const)) {
                throw new UnsupportedOperationException();
            }

            this.assign(((xbean.__.ClanRankList.Const)_o1_).nThis());
        }

    }

    private void assign(xbean.__.ClanRankList _o_) {
        this.records = new LinkedList();

        for(ClanRankRecord _v_ : xbean.__.ClanRankList.access$200(_o_)) {
            this.records.add(new xbean.__.ClanRankRecord.Data(_v_));
        }

    }

    private void assign(ClanRankList$Data _o_) {
        this.records = new LinkedList();

        for(ClanRankRecord _v_ : _o_.records) {
            this.records.add(new xbean.__.ClanRankRecord.Data(_v_));
        }

    }

    public final OctetsStream marshal(OctetsStream _os_) {
        _os_.compact_uint32(this.records.size());

        for(ClanRankRecord _v_ : this.records) {
            _v_.marshal(_os_);
        }

        return _os_;
    }

    public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            ClanRankRecord _v_ = Pod.newClanRankRecordData();
            _v_.unmarshal(_os_);
            this.records.add(_v_);
        }

        return _os_;
    }

    public ClanRankList copy() {
        return new ClanRankList$Data(this);
    }

    public ClanRankList toData() {
        return new ClanRankList$Data(this);
    }

    public ClanRankList toBean() {
        return new xbean.__.ClanRankList(this, (XBean)null, (String)null);
    }

    public ClanRankList toDataIf() {
        return this;
    }

    public ClanRankList toBeanIf() {
        return new xbean.__.ClanRankList(this, (XBean)null, (String)null);
    }

    public boolean xdbManaged() {
        throw new UnsupportedOperationException();
    }

    public Bean xdbParent() {
        throw new UnsupportedOperationException();
    }

    public String xdbVarname() {
        throw new UnsupportedOperationException();
    }

    public Long xdbObjId() {
        throw new UnsupportedOperationException();
    }

    public Bean toConst() {
        throw new UnsupportedOperationException();
    }

    public boolean isConst() {
        return false;
    }

    public boolean isData() {
        return true;
    }

    public List<ClanRankRecord> getRecords() {
        return this.records;
    }

    public List<ClanRankRecord> getRecordsAsData() {
        return this.records;
    }

    public final boolean equals(Object _o1_) {
        if (!(_o1_ instanceof ClanRankList$Data)) {
            return false;
        } else {
            ClanRankList$Data _o_ = (ClanRankList$Data)_o1_;
            return this.records.equals(_o_.records);
        }
    }

    public final int hashCode() {
        int _h_ = 0;
        _h_ += this.records.hashCode();
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.records);
        _sb_.append(")");
        return _sb_.toString();
    }
}
