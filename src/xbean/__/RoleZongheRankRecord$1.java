//
//

package xbean.__;

import mkdb.Log;
import mkdb.LogKey;
import mkdb.XBean;
import mkdb.Bean;

class RoleZongheRankRecord$1 extends LogKey {
    RoleZongheRankRecord$1(RoleZongheRankRecord this$0, XBean arg0, String arg1) {
        super(arg0, arg1);
        this.this$0 = this$0;
    }

    protected Log create() {
        return new RoleZongheRankRecord$1$1(this, this, RoleZongheRankRecord.access$1200(this.this$0));
    }
}
