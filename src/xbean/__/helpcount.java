//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import mkdb.Bean;
import mkdb.Log;
import mkdb.LogKey;
import mkdb.Logs;
import mkdb.XBean;
import mkdb.logs.Listenable;
import mkdb.logs.ListenableBean;
import mkdb.logs.ListenableChanged;
import mkdb.logs.LogInt;
import mkdb.logs.LogLong;
import com.locojoy.base.Octets;
import java.util.List;

public final class helpcount extends XBean implements xbean.helpcount {
    private long expvalue;
    private int shengwangvalue;
    private int clanvalue;
    private int clanhishelpnum;
    private int helpgiveitemnum;
    private int helpitemnum;

    public void _reset_unsafe_() {
        this.expvalue = 0L;
        this.shengwangvalue = 0;
        this.clanvalue = 0;
        this.clanhishelpnum = 0;
        this.helpgiveitemnum = 0;
        this.helpitemnum = 0;
    }

    helpcount(int __, XBean _xp_, String _vn_) {
        super(_xp_, _vn_);
    }

    public helpcount() {
        this(0, (XBean)null, (String)null);
    }

    public helpcount(helpcount _o_) {
        this(_o_, (XBean)null, (String)null);
    }

    helpcount(xbean.helpcount _o1_, XBean _xp_, String _vn_) {
        super(_xp_, _vn_);
        if (_o1_ instanceof helpcount) {
            this.assign((helpcount)_o1_);
        } else if (_o1_ instanceof Data) {
            this.assign((Data)_o1_);
        } else {
            if (!(_o1_ instanceof Const)) {
                throw new UnsupportedOperationException();
            }

            this.assign(((Const)_o1_).nThis());
        }

    }

    private void assign(helpcount _o_) {
        _o_._xdb_verify_unsafe_();
        this.expvalue = _o_.expvalue;
        this.shengwangvalue = _o_.shengwangvalue;
        this.clanvalue = _o_.clanvalue;
        this.clanhishelpnum = _o_.clanhishelpnum;
        this.helpgiveitemnum = _o_.helpgiveitemnum;
        this.helpitemnum = _o_.helpitemnum;
    }

    private void assign(Data _o_) {
        this.expvalue = _o_.expvalue;
        this.shengwangvalue = _o_.shengwangvalue;
        this.clanvalue = _o_.clanvalue;
        this.clanhishelpnum = _o_.clanhishelpnum;
        this.helpgiveitemnum = _o_.helpgiveitemnum;
        this.helpitemnum = _o_.helpitemnum;
    }

    public final OctetsStream marshal(OctetsStream _os_) {
        this._xdb_verify_unsafe_();
        _os_.marshal(this.expvalue);
        _os_.marshal(this.shengwangvalue);
        _os_.marshal(this.clanvalue);
        _os_.marshal(this.clanhishelpnum);
        _os_.marshal(this.helpgiveitemnum);
        _os_.marshal(this.helpitemnum);
        return _os_;
    }

    public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this._xdb_verify_unsafe_();
        this.expvalue = _os_.unmarshal_long();
        this.shengwangvalue = _os_.unmarshal_int();
        this.clanvalue = _os_.unmarshal_int();
        this.clanhishelpnum = _os_.unmarshal_int();
        this.helpgiveitemnum = _os_.unmarshal_int();
        this.helpitemnum = _os_.unmarshal_int();
        return _os_;
    }

    public xbean.helpcount copy() {
        this._xdb_verify_unsafe_();
        return new helpcount(this);
    }

    public xbean.helpcount toData() {
        this._xdb_verify_unsafe_();
        return new Data(this);
    }

    public xbean.helpcount toBean() {
        this._xdb_verify_unsafe_();
        return new helpcount(this);
    }

    public xbean.helpcount toDataIf() {
        this._xdb_verify_unsafe_();
        return new Data(this);
    }

    public xbean.helpcount toBeanIf() {
        this._xdb_verify_unsafe_();
        return this;
    }

    public Bean toConst() {
        this._xdb_verify_unsafe_();
        return new Const();
    }

    public long getExpvalue() {
        this._xdb_verify_unsafe_();
        return this.expvalue;
    }

    public int getShengwangvalue() {
        this._xdb_verify_unsafe_();
        return this.shengwangvalue;
    }

    public int getClanvalue() {
        this._xdb_verify_unsafe_();
        return this.clanvalue;
    }

    public int getClanhishelpnum() {
        this._xdb_verify_unsafe_();
        return this.clanhishelpnum;
    }

    public int getHelpgiveitemnum() {
        this._xdb_verify_unsafe_();
        return this.helpgiveitemnum;
    }

    public int getHelpitemnum() {
        this._xdb_verify_unsafe_();
        return this.helpitemnum;
    }

    public void setExpvalue(long _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "expvalue") {
            protected Log create() {
                return new LogLong(this, helpcount.this.expvalue) {
                    public void rollback() {
                        helpcount.this.expvalue = this._xdb_saved;
                    }
                };
            }
        });
        this.expvalue = _v_;
    }

    public void setShengwangvalue(int _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "shengwangvalue") {
            protected Log create() {
                return new LogInt(this, helpcount.this.shengwangvalue) {
                    public void rollback() {
                        helpcount.this.shengwangvalue = this._xdb_saved;
                    }
                };
            }
        });
        this.shengwangvalue = _v_;
    }

    public void setClanvalue(int _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "clanvalue") {
            protected Log create() {
                return new LogInt(this, helpcount.this.clanvalue) {
                    public void rollback() {
                        helpcount.this.clanvalue = this._xdb_saved;
                    }
                };
            }
        });
        this.clanvalue = _v_;
    }

    public void setClanhishelpnum(int _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "clanhishelpnum") {
            protected Log create() {
                return new LogInt(this, helpcount.this.clanhishelpnum) {
                    public void rollback() {
                        helpcount.this.clanhishelpnum = this._xdb_saved;
                    }
                };
            }
        });
        this.clanhishelpnum = _v_;
    }

    public void setHelpgiveitemnum(int _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "helpgiveitemnum") {
            protected Log create() {
                return new LogInt(this, helpcount.this.helpgiveitemnum) {
                    public void rollback() {
                        helpcount.this.helpgiveitemnum = this._xdb_saved;
                    }
                };
            }
        });
        this.helpgiveitemnum = _v_;
    }

    public void setHelpitemnum(int _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "helpitemnum") {
            protected Log create() {
                return new LogInt(this, helpcount.this.helpitemnum) {
                    public void rollback() {
                        helpcount.this.helpitemnum = this._xdb_saved;
                    }
                };
            }
        });
        this.helpitemnum = _v_;
    }

    public final boolean equals(Object _o1_) {
        this._xdb_verify_unsafe_();
        helpcount _o_ = null;
        if (_o1_ instanceof helpcount) {
            _o_ = (helpcount)_o1_;
        } else {
            if (!(_o1_ instanceof Const)) {
                return false;
            }

            _o_ = ((Const)_o1_).nThis();
        }

        if (this.expvalue != _o_.expvalue) {
            return false;
        } else if (this.shengwangvalue != _o_.shengwangvalue) {
            return false;
        } else if (this.clanvalue != _o_.clanvalue) {
            return false;
        } else if (this.clanhishelpnum != _o_.clanhishelpnum) {
            return false;
        } else if (this.helpgiveitemnum != _o_.helpgiveitemnum) {
            return false;
        } else {
            return this.helpitemnum == _o_.helpitemnum;
        }
    }

    public final int hashCode() {
        this._xdb_verify_unsafe_();
        int _h_ = 0;
        _h_ = (int)((long)_h_ + this.expvalue);
        _h_ += this.shengwangvalue;
        _h_ += this.clanvalue;
        _h_ += this.clanhishelpnum;
        _h_ += this.helpgiveitemnum;
        _h_ += this.helpitemnum;
        return _h_;
    }

    public String toString() {
        this._xdb_verify_unsafe_();
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.expvalue);
        _sb_.append(",");
        _sb_.append(this.shengwangvalue);
        _sb_.append(",");
        _sb_.append(this.clanvalue);
        _sb_.append(",");
        _sb_.append(this.clanhishelpnum);
        _sb_.append(",");
        _sb_.append(this.helpgiveitemnum);
        _sb_.append(",");
        _sb_.append(this.helpitemnum);
        _sb_.append(")");
        return _sb_.toString();
    }

    public Listenable newListenable() {
        ListenableBean lb = new ListenableBean();
        lb.add((new ListenableChanged()).setVarName("expvalue"));
        lb.add((new ListenableChanged()).setVarName("shengwangvalue"));
        lb.add((new ListenableChanged()).setVarName("clanvalue"));
        lb.add((new ListenableChanged()).setVarName("clanhishelpnum"));
        lb.add((new ListenableChanged()).setVarName("helpgiveitemnum"));
        lb.add((new ListenableChanged()).setVarName("helpitemnum"));
        return lb;
    }

    private class Const implements xbean.helpcount {
        private Const() {
        }

        helpcount nThis() {
            return helpcount.this;
        }

        public void _reset_unsafe_() {
            throw new UnsupportedOperationException();
        }

        public xbean.helpcount copy() {
            return helpcount.this.copy();
        }

        public xbean.helpcount toData() {
            return helpcount.this.toData();
        }

        public xbean.helpcount toBean() {
            return helpcount.this.toBean();
        }

        public xbean.helpcount toDataIf() {
            return helpcount.this.toDataIf();
        }

        public xbean.helpcount toBeanIf() {
            return helpcount.this.toBeanIf();
        }

        public long getExpvalue() {
            helpcount.this._xdb_verify_unsafe_();
            return helpcount.this.expvalue;
        }

        public int getShengwangvalue() {
            helpcount.this._xdb_verify_unsafe_();
            return helpcount.this.shengwangvalue;
        }

        public int getClanvalue() {
            helpcount.this._xdb_verify_unsafe_();
            return helpcount.this.clanvalue;
        }

        public int getClanhishelpnum() {
            helpcount.this._xdb_verify_unsafe_();
            return helpcount.this.clanhishelpnum;
        }

        public int getHelpgiveitemnum() {
            helpcount.this._xdb_verify_unsafe_();
            return helpcount.this.helpgiveitemnum;
        }

        public int getHelpitemnum() {
            helpcount.this._xdb_verify_unsafe_();
            return helpcount.this.helpitemnum;
        }

        public void setExpvalue(long _v_) {
            helpcount.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setShengwangvalue(int _v_) {
            helpcount.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setClanvalue(int _v_) {
            helpcount.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setClanhishelpnum(int _v_) {
            helpcount.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setHelpgiveitemnum(int _v_) {
            helpcount.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setHelpitemnum(int _v_) {
            helpcount.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public Bean toConst() {
            helpcount.this._xdb_verify_unsafe_();
            return this;
        }

        public boolean isConst() {
            helpcount.this._xdb_verify_unsafe_();
            return true;
        }

        public boolean isData() {
            return helpcount.this.isData();
        }

        public OctetsStream marshal(OctetsStream _os_) {
            return helpcount.this.marshal(_os_);
        }

        public OctetsStream unmarshal(OctetsStream arg0) throws MarshalException {
            helpcount.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public Bean xdbParent() {
            return helpcount.this.xdbParent();
        }

        public boolean xdbManaged() {
            return helpcount.this.xdbManaged();
        }

        public String xdbVarname() {
            return helpcount.this.xdbVarname();
        }

        public Long xdbObjId() {
            return helpcount.this.xdbObjId();
        }

        public boolean equals(Object obj) {
            return helpcount.this.equals(obj);
        }

        public int hashCode() {
            return helpcount.this.hashCode();
        }

        public String toString() {
            return helpcount.this.toString();
        }
    }

    public static final class Data implements xbean.helpcount {
        private long expvalue;
        private int shengwangvalue;
        private int clanvalue;
        private int clanhishelpnum;
        private int helpgiveitemnum;
        private int helpitemnum;

        public void _reset_unsafe_() {
            throw new UnsupportedOperationException();
        }

        public Data() {
        }

        Data(xbean.helpcount _o1_) {
            if (_o1_ instanceof helpcount) {
                this.assign((helpcount)_o1_);
            } else if (_o1_ instanceof Data) {
                this.assign((Data)_o1_);
            } else {
                if (!(_o1_ instanceof Const)) {
                    throw new UnsupportedOperationException();
                }

                this.assign(((Const)_o1_).nThis());
            }

        }

        private void assign(helpcount _o_) {
            this.expvalue = _o_.expvalue;
            this.shengwangvalue = _o_.shengwangvalue;
            this.clanvalue = _o_.clanvalue;
            this.clanhishelpnum = _o_.clanhishelpnum;
            this.helpgiveitemnum = _o_.helpgiveitemnum;
            this.helpitemnum = _o_.helpitemnum;
        }

        private void assign(Data _o_) {
            this.expvalue = _o_.expvalue;
            this.shengwangvalue = _o_.shengwangvalue;
            this.clanvalue = _o_.clanvalue;
            this.clanhishelpnum = _o_.clanhishelpnum;
            this.helpgiveitemnum = _o_.helpgiveitemnum;
            this.helpitemnum = _o_.helpitemnum;
        }

        public final OctetsStream marshal(OctetsStream _os_) {
            _os_.marshal(this.expvalue);
            _os_.marshal(this.shengwangvalue);
            _os_.marshal(this.clanvalue);
            _os_.marshal(this.clanhishelpnum);
            _os_.marshal(this.helpgiveitemnum);
            _os_.marshal(this.helpitemnum);
            return _os_;
        }

        public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
            this.expvalue = _os_.unmarshal_long();
            this.shengwangvalue = _os_.unmarshal_int();
            this.clanvalue = _os_.unmarshal_int();
            this.clanhishelpnum = _os_.unmarshal_int();
            this.helpgiveitemnum = _os_.unmarshal_int();
            this.helpitemnum = _os_.unmarshal_int();
            return _os_;
        }

        public xbean.helpcount copy() {
            return new Data(this);
        }

        public xbean.helpcount toData() {
            return new Data(this);
        }

        public xbean.helpcount toBean() {
            return new helpcount(this, (XBean)null, (String)null);
        }

        public xbean.helpcount toDataIf() {
            return this;
        }

        public xbean.helpcount toBeanIf() {
            return new helpcount(this, (XBean)null, (String)null);
        }

        public boolean xdbManaged() {
            throw new UnsupportedOperationException();
        }

        public Bean xdbParent() {
            throw new UnsupportedOperationException();
        }

        public String xdbVarname() {
            throw new UnsupportedOperationException();
        }

        public Long xdbObjId() {
            throw new UnsupportedOperationException();
        }

        public Bean toConst() {
            throw new UnsupportedOperationException();
        }

        public boolean isConst() {
            return false;
        }

        public boolean isData() {
            return true;
        }

        public long getExpvalue() {
            return this.expvalue;
        }

        public int getShengwangvalue() {
            return this.shengwangvalue;
        }

        public int getClanvalue() {
            return this.clanvalue;
        }

        public int getClanhishelpnum() {
            return this.clanhishelpnum;
        }

        public int getHelpgiveitemnum() {
            return this.helpgiveitemnum;
        }

        public int getHelpitemnum() {
            return this.helpitemnum;
        }

        public void setExpvalue(long _v_) {
            this.expvalue = _v_;
        }

        public void setShengwangvalue(int _v_) {
            this.shengwangvalue = _v_;
        }

        public void setClanvalue(int _v_) {
            this.clanvalue = _v_;
        }

        public void setClanhishelpnum(int _v_) {
            this.clanhishelpnum = _v_;
        }

        public void setHelpgiveitemnum(int _v_) {
            this.helpgiveitemnum = _v_;
        }

        public void setHelpitemnum(int _v_) {
            this.helpitemnum = _v_;
        }

        public final boolean equals(Object _o1_) {
            if (!(_o1_ instanceof Data)) {
                return false;
            } else {
                Data _o_ = (Data)_o1_;
                if (this.expvalue != _o_.expvalue) {
                    return false;
                } else if (this.shengwangvalue != _o_.shengwangvalue) {
                    return false;
                } else if (this.clanvalue != _o_.clanvalue) {
                    return false;
                } else if (this.clanhishelpnum != _o_.clanhishelpnum) {
                    return false;
                } else if (this.helpgiveitemnum != _o_.helpgiveitemnum) {
                    return false;
                } else {
                    return this.helpitemnum == _o_.helpitemnum;
                }
            }
        }

        public final int hashCode() {
            int _h_ = 0;
            _h_ = (int)((long)_h_ + this.expvalue);
            _h_ += this.shengwangvalue;
            _h_ += this.clanvalue;
            _h_ += this.clanhishelpnum;
            _h_ += this.helpgiveitemnum;
            _h_ += this.helpitemnum;
            return _h_;
        }

        public String toString() {
            StringBuilder _sb_ = new StringBuilder();
            _sb_.append("(");
            _sb_.append(this.expvalue);
            _sb_.append(",");
            _sb_.append(this.shengwangvalue);
            _sb_.append(",");
            _sb_.append(this.clanvalue);
            _sb_.append(",");
            _sb_.append(this.clanhishelpnum);
            _sb_.append(",");
            _sb_.append(this.helpgiveitemnum);
            _sb_.append(",");
            _sb_.append(this.helpitemnum);
            _sb_.append(")");
            return _sb_.toString();
        }
    }
}
