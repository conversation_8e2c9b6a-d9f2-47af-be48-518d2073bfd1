//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import java.util.HashMap;
import java.util.Map;
import mkdb.Bean;
import mkdb.Consts;
import xbean.RoleYaoQianShuInfo;
import xbean.RoleYaoQianShuInfoMaps;
import com.locojoy.base.Octets;
import java.util.Set;

class RoleYaoQianShuInfoMaps$Const implements RoleYaoQianShuInfoMaps {
    private RoleYaoQianShuInfoMaps$Const(xbean.__.RoleYaoQianShuInfoMaps var1) {
        this.this$0 = var1;
    }

    xbean.__.RoleYaoQianShuInfoMaps nThis() {
        return this.this$0;
    }

    public void _reset_unsafe_() {
        throw new UnsupportedOperationException();
    }

    public RoleYaoQianShuInfoMaps copy() {
        return this.this$0.copy();
    }

    public RoleYaoQianShuInfoMaps toData() {
        return this.this$0.toData();
    }

    public RoleYaoQianShuInfoMaps toBean() {
        return this.this$0.toBean();
    }

    public RoleYaoQianShuInfoMaps toDataIf() {
        return this.this$0.toDataIf();
    }

    public RoleYaoQianShuInfoMaps toBeanIf() {
        return this.this$0.toBeanIf();
    }

    public Map<Long, RoleYaoQianShuInfo> getYaoqianshumaps() {
        this.this$0._xdb_verify_unsafe_();
        return Consts.constMap(xbean.__.RoleYaoQianShuInfoMaps.access$200(this.this$0));
    }

    public Map<Long, RoleYaoQianShuInfo> getYaoqianshumapsAsData() {
        this.this$0._xdb_verify_unsafe_();
        xbean.__.RoleYaoQianShuInfoMaps _o_ = this.this$0;
        Map<Long, RoleYaoQianShuInfo> yaoqianshumaps = new HashMap();

        for(Map.Entry<Long, RoleYaoQianShuInfo> _e_ : xbean.__.RoleYaoQianShuInfoMaps.access$200(_o_).entrySet()) {
            yaoqianshumaps.put(_e_.getKey(), new xbean.__.RoleYaoQianShuInfo.Data((RoleYaoQianShuInfo)_e_.getValue()));
        }

        return yaoqianshumaps;
    }

    public Bean toConst() {
        this.this$0._xdb_verify_unsafe_();
        return this;
    }

    public boolean isConst() {
        this.this$0._xdb_verify_unsafe_();
        return true;
    }

    public boolean isData() {
        return this.this$0.isData();
    }

    public OctetsStream marshal(OctetsStream _os_) {
        return this.this$0.marshal(_os_);
    }

    public OctetsStream unmarshal(OctetsStream arg0) throws MarshalException {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public Bean xdbParent() {
        return this.this$0.xdbParent();
    }

    public boolean xdbManaged() {
        return this.this$0.xdbManaged();
    }

    public String xdbVarname() {
        return this.this$0.xdbVarname();
    }

    public Long xdbObjId() {
        return this.this$0.xdbObjId();
    }

    public boolean equals(Object obj) {
        return this.this$0.equals(obj);
    }

    public int hashCode() {
        return this.this$0.hashCode();
    }

    public String toString() {
        return this.this$0.toString();
    }
}
