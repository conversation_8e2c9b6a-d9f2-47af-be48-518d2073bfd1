//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import java.util.LinkedList;
import java.util.List;
import mkdb.Bean;
import mkdb.Consts;
import mkdb.Log;
import mkdb.LogKey;
import mkdb.Logs;
import mkdb.XBean;
import mkdb.logs.Listenable;
import mkdb.logs.ListenableBean;
import mkdb.logs.ListenableChanged;
import mkdb.logs.LogObject;
import xbean.Pod;
import xbean.TeamInvite;
import com.locojoy.base.Octets;

public final class InviteInfo extends XBean implements xbean.InviteInfo {
    private boolean beinginvited;
    private TeamInvite inviting;
    private LinkedList<TeamInvite> invited;

    public void _reset_unsafe_() {
        this.beinginvited = false;
        this.inviting._reset_unsafe_();
        this.invited.clear();
    }

    InviteInfo(int __, XBean _xp_, String _vn_) {
        super(_xp_, _vn_);
        this.inviting = new xbean.__.TeamInvite(0, this, "inviting");
        this.invited = new LinkedList();
    }

    public InviteInfo() {
        this(0, (XBean)null, (String)null);
    }

    public InviteInfo(InviteInfo _o_) {
        this(_o_, (XBean)null, (String)null);
    }

    InviteInfo(xbean.InviteInfo _o1_, XBean _xp_, String _vn_) {
        super(_xp_, _vn_);
        if (_o1_ instanceof InviteInfo) {
            this.assign((InviteInfo)_o1_);
        } else if (_o1_ instanceof Data) {
            this.assign((Data)_o1_);
        } else {
            if (!(_o1_ instanceof Const)) {
                throw new UnsupportedOperationException();
            }

            this.assign(((Const)_o1_).nThis());
        }

    }

    private void assign(InviteInfo _o_) {
        _o_._xdb_verify_unsafe_();
        this.beinginvited = _o_.beinginvited;
        this.inviting = new xbean.__.TeamInvite(_o_.inviting, this, "inviting");
        this.invited = new LinkedList();

        for(TeamInvite _v_ : _o_.invited) {
            this.invited.add(new xbean.__.TeamInvite(_v_, this, "invited"));
        }

    }

    private void assign(Data _o_) {
        this.beinginvited = _o_.beinginvited;
        this.inviting = new xbean.__.TeamInvite(_o_.inviting, this, "inviting");
        this.invited = new LinkedList();

        for(TeamInvite _v_ : _o_.invited) {
            this.invited.add(new xbean.__.TeamInvite(_v_, this, "invited"));
        }

    }

    public final OctetsStream marshal(OctetsStream _os_) {
        this._xdb_verify_unsafe_();
        _os_.marshal(this.beinginvited);
        this.inviting.marshal(_os_);
        _os_.compact_uint32(this.invited.size());

        for(TeamInvite _v_ : this.invited) {
            _v_.marshal(_os_);
        }

        return _os_;
    }

    public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this._xdb_verify_unsafe_();
        this.beinginvited = _os_.unmarshal_boolean();
        this.inviting.unmarshal(_os_);

        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            TeamInvite _v_ = new xbean.__.TeamInvite(0, this, "invited");
            _v_.unmarshal(_os_);
            this.invited.add(_v_);
        }

        return _os_;
    }

    public xbean.InviteInfo copy() {
        this._xdb_verify_unsafe_();
        return new InviteInfo(this);
    }

    public xbean.InviteInfo toData() {
        this._xdb_verify_unsafe_();
        return new Data(this);
    }

    public xbean.InviteInfo toBean() {
        this._xdb_verify_unsafe_();
        return new InviteInfo(this);
    }

    public xbean.InviteInfo toDataIf() {
        this._xdb_verify_unsafe_();
        return new Data(this);
    }

    public xbean.InviteInfo toBeanIf() {
        this._xdb_verify_unsafe_();
        return this;
    }

    public Bean toConst() {
        this._xdb_verify_unsafe_();
        return new Const();
    }

    public boolean getBeinginvited() {
        this._xdb_verify_unsafe_();
        return this.beinginvited;
    }

    public TeamInvite getInviting() {
        this._xdb_verify_unsafe_();
        return this.inviting;
    }

    public List<TeamInvite> getInvited() {
        this._xdb_verify_unsafe_();
        return Logs.logList(new LogKey(this, "invited"), this.invited);
    }

    public List<TeamInvite> getInvitedAsData() {
        this._xdb_verify_unsafe_();
        List<TeamInvite> invited = new LinkedList();

        for(TeamInvite _v_ : this.invited) {
            invited.add(new xbean.__.TeamInvite.Data(_v_));
        }

        return invited;
    }

    public void setBeinginvited(boolean _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "beinginvited") {
            protected Log create() {
                return new LogObject<Boolean>(this, InviteInfo.this.beinginvited) {
                    public void rollback() {
                        InviteInfo.this.beinginvited = (Boolean)this._xdb_saved;
                    }
                };
            }
        });
        this.beinginvited = _v_;
    }

    public final boolean equals(Object _o1_) {
        this._xdb_verify_unsafe_();
        InviteInfo _o_ = null;
        if (_o1_ instanceof InviteInfo) {
            _o_ = (InviteInfo)_o1_;
        } else {
            if (!(_o1_ instanceof Const)) {
                return false;
            }

            _o_ = ((Const)_o1_).nThis();
        }

        if (this.beinginvited != _o_.beinginvited) {
            return false;
        } else if (!this.inviting.equals(_o_.inviting)) {
            return false;
        } else {
            return this.invited.equals(_o_.invited);
        }
    }

    public final int hashCode() {
        this._xdb_verify_unsafe_();
        int _h_ = 0;
        _h_ += this.beinginvited ? 1231 : 1237;
        _h_ += this.inviting.hashCode();
        _h_ += this.invited.hashCode();
        return _h_;
    }

    public String toString() {
        this._xdb_verify_unsafe_();
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.beinginvited);
        _sb_.append(",");
        _sb_.append(this.inviting);
        _sb_.append(",");
        _sb_.append(this.invited);
        _sb_.append(")");
        return _sb_.toString();
    }

    public Listenable newListenable() {
        ListenableBean lb = new ListenableBean();
        lb.add((new ListenableChanged()).setVarName("beinginvited"));
        lb.add((new ListenableChanged()).setVarName("inviting"));
        lb.add((new ListenableChanged()).setVarName("invited"));
        return lb;
    }

    private class Const implements xbean.InviteInfo {
        private Const() {
        }

        InviteInfo nThis() {
            return InviteInfo.this;
        }

        public void _reset_unsafe_() {
            throw new UnsupportedOperationException();
        }

        public xbean.InviteInfo copy() {
            return InviteInfo.this.copy();
        }

        public xbean.InviteInfo toData() {
            return InviteInfo.this.toData();
        }

        public xbean.InviteInfo toBean() {
            return InviteInfo.this.toBean();
        }

        public xbean.InviteInfo toDataIf() {
            return InviteInfo.this.toDataIf();
        }

        public xbean.InviteInfo toBeanIf() {
            return InviteInfo.this.toBeanIf();
        }

        public boolean getBeinginvited() {
            InviteInfo.this._xdb_verify_unsafe_();
            return InviteInfo.this.beinginvited;
        }

        public TeamInvite getInviting() {
            InviteInfo.this._xdb_verify_unsafe_();
            return (TeamInvite)Consts.toConst(InviteInfo.this.inviting);
        }

        public List<TeamInvite> getInvited() {
            InviteInfo.this._xdb_verify_unsafe_();
            return Consts.constList(InviteInfo.this.invited);
        }

        public List<TeamInvite> getInvitedAsData() {
            InviteInfo.this._xdb_verify_unsafe_();
            InviteInfo _o_ = InviteInfo.this;
            List<TeamInvite> invited = new LinkedList();

            for(TeamInvite _v_ : _o_.invited) {
                invited.add(new xbean.__.TeamInvite.Data(_v_));
            }

            return invited;
        }

        public void setBeinginvited(boolean _v_) {
            InviteInfo.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public Bean toConst() {
            InviteInfo.this._xdb_verify_unsafe_();
            return this;
        }

        public boolean isConst() {
            InviteInfo.this._xdb_verify_unsafe_();
            return true;
        }

        public boolean isData() {
            return InviteInfo.this.isData();
        }

        public OctetsStream marshal(OctetsStream _os_) {
            return InviteInfo.this.marshal(_os_);
        }

        public OctetsStream unmarshal(OctetsStream arg0) throws MarshalException {
            InviteInfo.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public Bean xdbParent() {
            return InviteInfo.this.xdbParent();
        }

        public boolean xdbManaged() {
            return InviteInfo.this.xdbManaged();
        }

        public String xdbVarname() {
            return InviteInfo.this.xdbVarname();
        }

        public Long xdbObjId() {
            return InviteInfo.this.xdbObjId();
        }

        public boolean equals(Object obj) {
            return InviteInfo.this.equals(obj);
        }

        public int hashCode() {
            return InviteInfo.this.hashCode();
        }

        public String toString() {
            return InviteInfo.this.toString();
        }
    }

    public static final class Data implements xbean.InviteInfo {
        private boolean beinginvited;
        private TeamInvite inviting;
        private LinkedList<TeamInvite> invited;

        public void _reset_unsafe_() {
            throw new UnsupportedOperationException();
        }

        public Data() {
            this.inviting = new xbean.__.TeamInvite.Data();
            this.invited = new LinkedList();
        }

        Data(xbean.InviteInfo _o1_) {
            if (_o1_ instanceof InviteInfo) {
                this.assign((InviteInfo)_o1_);
            } else if (_o1_ instanceof Data) {
                this.assign((Data)_o1_);
            } else {
                if (!(_o1_ instanceof Const)) {
                    throw new UnsupportedOperationException();
                }

                this.assign(((Const)_o1_).nThis());
            }

        }

        private void assign(InviteInfo _o_) {
            this.beinginvited = _o_.beinginvited;
            this.inviting = new xbean.__.TeamInvite.Data(_o_.inviting);
            this.invited = new LinkedList();

            for(TeamInvite _v_ : _o_.invited) {
                this.invited.add(new xbean.__.TeamInvite.Data(_v_));
            }

        }

        private void assign(Data _o_) {
            this.beinginvited = _o_.beinginvited;
            this.inviting = new xbean.__.TeamInvite.Data(_o_.inviting);
            this.invited = new LinkedList();

            for(TeamInvite _v_ : _o_.invited) {
                this.invited.add(new xbean.__.TeamInvite.Data(_v_));
            }

        }

        public final OctetsStream marshal(OctetsStream _os_) {
            _os_.marshal(this.beinginvited);
            this.inviting.marshal(_os_);
            _os_.compact_uint32(this.invited.size());

            for(TeamInvite _v_ : this.invited) {
                _v_.marshal(_os_);
            }

            return _os_;
        }

        public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
            this.beinginvited = _os_.unmarshal_boolean();
            this.inviting.unmarshal(_os_);

            for(int size = _os_.uncompact_uint32(); size > 0; --size) {
                TeamInvite _v_ = Pod.newTeamInviteData();
                _v_.unmarshal(_os_);
                this.invited.add(_v_);
            }

            return _os_;
        }

        public xbean.InviteInfo copy() {
            return new Data(this);
        }

        public xbean.InviteInfo toData() {
            return new Data(this);
        }

        public xbean.InviteInfo toBean() {
            return new InviteInfo(this, (XBean)null, (String)null);
        }

        public xbean.InviteInfo toDataIf() {
            return this;
        }

        public xbean.InviteInfo toBeanIf() {
            return new InviteInfo(this, (XBean)null, (String)null);
        }

        public boolean xdbManaged() {
            throw new UnsupportedOperationException();
        }

        public Bean xdbParent() {
            throw new UnsupportedOperationException();
        }

        public String xdbVarname() {
            throw new UnsupportedOperationException();
        }

        public Long xdbObjId() {
            throw new UnsupportedOperationException();
        }

        public Bean toConst() {
            throw new UnsupportedOperationException();
        }

        public boolean isConst() {
            return false;
        }

        public boolean isData() {
            return true;
        }

        public boolean getBeinginvited() {
            return this.beinginvited;
        }

        public TeamInvite getInviting() {
            return this.inviting;
        }

        public List<TeamInvite> getInvited() {
            return this.invited;
        }

        public List<TeamInvite> getInvitedAsData() {
            return this.invited;
        }

        public void setBeinginvited(boolean _v_) {
            this.beinginvited = _v_;
        }

        public final boolean equals(Object _o1_) {
            if (!(_o1_ instanceof Data)) {
                return false;
            } else {
                Data _o_ = (Data)_o1_;
                if (this.beinginvited != _o_.beinginvited) {
                    return false;
                } else if (!this.inviting.equals(_o_.inviting)) {
                    return false;
                } else {
                    return this.invited.equals(_o_.invited);
                }
            }
        }

        public final int hashCode() {
            int _h_ = 0;
            _h_ += this.beinginvited ? 1231 : 1237;
            _h_ += this.inviting.hashCode();
            _h_ += this.invited.hashCode();
            return _h_;
        }

        public String toString() {
            StringBuilder _sb_ = new StringBuilder();
            _sb_.append("(");
            _sb_.append(this.beinginvited);
            _sb_.append(",");
            _sb_.append(this.inviting);
            _sb_.append(",");
            _sb_.append(this.invited);
            _sb_.append(")");
            return _sb_.toString();
        }
    }
}
