//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import mkdb.Bean;
import mkdb.XBean;
import xbean.PracticeSkill;
import com.locojoy.base.Octets;

public final class PracticeSkill$Data implements PracticeSkill {
    private int level;
    private int exp;

    public void _reset_unsafe_() {
        throw new UnsupportedOperationException();
    }

    public PracticeSkill$Data() {
    }

    PracticeSkill$Data(PracticeSkill _o1_) {
        if (_o1_ instanceof xbean.__.PracticeSkill) {
            this.assign((xbean.__.PracticeSkill)_o1_);
        } else if (_o1_ instanceof PracticeSkill$Data) {
            this.assign((PracticeSkill$Data)_o1_);
        } else {
            if (!(_o1_ instanceof xbean.__.PracticeSkill.Const)) {
                throw new UnsupportedOperationException();
            }

            this.assign(((xbean.__.PracticeSkill.Const)_o1_).nThis());
        }

    }

    private void assign(xbean.__.PracticeSkill _o_) {
        this.level = xbean.__.PracticeSkill.access$300(_o_);
        this.exp = xbean.__.PracticeSkill.access$400(_o_);
    }

    private void assign(PracticeSkill$Data _o_) {
        this.level = _o_.level;
        this.exp = _o_.exp;
    }

    public final OctetsStream marshal(OctetsStream _os_) {
        _os_.marshal(this.level);
        _os_.marshal(this.exp);
        return _os_;
    }

    public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.level = _os_.unmarshal_int();
        this.exp = _os_.unmarshal_int();
        return _os_;
    }

    public PracticeSkill copy() {
        return new PracticeSkill$Data(this);
    }

    public PracticeSkill toData() {
        return new PracticeSkill$Data(this);
    }

    public PracticeSkill toBean() {
        return new xbean.__.PracticeSkill(this, (XBean)null, (String)null);
    }

    public PracticeSkill toDataIf() {
        return this;
    }

    public PracticeSkill toBeanIf() {
        return new xbean.__.PracticeSkill(this, (XBean)null, (String)null);
    }

    public boolean xdbManaged() {
        throw new UnsupportedOperationException();
    }

    public Bean xdbParent() {
        throw new UnsupportedOperationException();
    }

    public String xdbVarname() {
        throw new UnsupportedOperationException();
    }

    public Long xdbObjId() {
        throw new UnsupportedOperationException();
    }

    public Bean toConst() {
        throw new UnsupportedOperationException();
    }

    public boolean isConst() {
        return false;
    }

    public boolean isData() {
        return true;
    }

    public int getLevel() {
        return this.level;
    }

    public int getExp() {
        return this.exp;
    }

    public void setLevel(int _v_) {
        this.level = _v_;
    }

    public void setExp(int _v_) {
        this.exp = _v_;
    }

    public final boolean equals(Object _o1_) {
        if (!(_o1_ instanceof PracticeSkill$Data)) {
            return false;
        } else {
            PracticeSkill$Data _o_ = (PracticeSkill$Data)_o1_;
            if (this.level != _o_.level) {
                return false;
            } else {
                return this.exp == _o_.exp;
            }
        }
    }

    public final int hashCode() {
        int _h_ = 0;
        _h_ += this.level;
        _h_ += this.exp;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.level);
        _sb_.append(",");
        _sb_.append(this.exp);
        _sb_.append(")");
        return _sb_.toString();
    }
}
