//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import mkdb.Bean;
import xbean.ChargeRecord;
import com.locojoy.base.Octets;

class ChargeRecord$Const implements ChargeRecord {
    private ChargeRecord$Const(xbean.__.ChargeRecord var1) {
        this.this$0 = var1;
    }

    xbean.__.ChargeRecord nThis() {
        return this.this$0;
    }

    public void _reset_unsafe_() {
        throw new UnsupportedOperationException();
    }

    public ChargeRecord copy() {
        return this.this$0.copy();
    }

    public ChargeRecord toData() {
        return this.this$0.toData();
    }

    public ChargeRecord toBean() {
        return this.this$0.toBean();
    }

    public ChargeRecord toDataIf() {
        return this.this$0.toDataIf();
    }

    public ChargeRecord toBeanIf() {
        return this.this$0.toBeanIf();
    }

    public int getChargecount() {
        this.this$0._xdb_verify_unsafe_();
        return xbean.__.ChargeRecord.access$300(this.this$0);
    }

    public long getChargetime() {
        this.this$0._xdb_verify_unsafe_();
        return xbean.__.ChargeRecord.access$400(this.this$0);
    }

    public void setChargecount(int _v_) {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public void setChargetime(long _v_) {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public Bean toConst() {
        this.this$0._xdb_verify_unsafe_();
        return this;
    }

    public boolean isConst() {
        this.this$0._xdb_verify_unsafe_();
        return true;
    }

    public boolean isData() {
        return this.this$0.isData();
    }

    public OctetsStream marshal(OctetsStream _os_) {
        return this.this$0.marshal(_os_);
    }

    public OctetsStream unmarshal(OctetsStream arg0) throws MarshalException {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public Bean xdbParent() {
        return this.this$0.xdbParent();
    }

    public boolean xdbManaged() {
        return this.this$0.xdbManaged();
    }

    public String xdbVarname() {
        return this.this$0.xdbVarname();
    }

    public Long xdbObjId() {
        return this.this$0.xdbObjId();
    }

    public boolean equals(Object obj) {
        return this.this$0.equals(obj);
    }

    public int hashCode() {
        return this.this$0.hashCode();
    }

    public String toString() {
        return this.this$0.toString();
    }
}
