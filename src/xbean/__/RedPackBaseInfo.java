//
//

package xbean.__;

import com.locojoy.base.Octets;
import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import mkdb.Bean;
import mkdb.Log;
import mkdb.LogKey;
import mkdb.Logs;
import mkdb.XBean;
import mkdb.logs.Listenable;
import mkdb.logs.ListenableBean;
import mkdb.logs.ListenableChanged;
import mkdb.logs.LogLong;
import mkdb.logs.LogString;
import java.util.List;

public final class RedPackBaseInfo extends XBean implements xbean.RedPackBaseInfo {
    private long roleid;
    private String redpackid;
    private long sendtime;

    public void _reset_unsafe_() {
        this.roleid = 0L;
        this.redpackid = "";
        this.sendtime = 0L;
    }

    RedPackBaseInfo(int __, XBean _xp_, String _vn_) {
        super(_xp_, _vn_);
        this.redpackid = "";
    }

    public RedPackBaseInfo() {
        this(0, (XBean)null, (String)null);
    }

    public RedPackBaseInfo(RedPackBaseInfo _o_) {
        this(_o_, (XBean)null, (String)null);
    }

    RedPackBaseInfo(xbean.RedPackBaseInfo _o1_, XBean _xp_, String _vn_) {
        super(_xp_, _vn_);
        if (_o1_ instanceof RedPackBaseInfo) {
            this.assign((RedPackBaseInfo)_o1_);
        } else if (_o1_ instanceof Data) {
            this.assign((Data)_o1_);
        } else {
            if (!(_o1_ instanceof Const)) {
                throw new UnsupportedOperationException();
            }

            this.assign(((Const)_o1_).nThis());
        }

    }

    private void assign(RedPackBaseInfo _o_) {
        _o_._xdb_verify_unsafe_();
        this.roleid = _o_.roleid;
        this.redpackid = _o_.redpackid;
        this.sendtime = _o_.sendtime;
    }

    private void assign(Data _o_) {
        this.roleid = _o_.roleid;
        this.redpackid = _o_.redpackid;
        this.sendtime = _o_.sendtime;
    }

    public final OctetsStream marshal(OctetsStream _os_) {
        this._xdb_verify_unsafe_();
        _os_.marshal(this.roleid);
        _os_.marshal(this.redpackid, "UTF-16LE");
        _os_.marshal(this.sendtime);
        return _os_;
    }

    public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this._xdb_verify_unsafe_();
        this.roleid = _os_.unmarshal_long();
        this.redpackid = _os_.unmarshal_String("UTF-16LE");
        this.sendtime = _os_.unmarshal_long();
        return _os_;
    }

    public xbean.RedPackBaseInfo copy() {
        this._xdb_verify_unsafe_();
        return new RedPackBaseInfo(this);
    }

    public xbean.RedPackBaseInfo toData() {
        this._xdb_verify_unsafe_();
        return new Data(this);
    }

    public xbean.RedPackBaseInfo toBean() {
        this._xdb_verify_unsafe_();
        return new RedPackBaseInfo(this);
    }

    public xbean.RedPackBaseInfo toDataIf() {
        this._xdb_verify_unsafe_();
        return new Data(this);
    }

    public xbean.RedPackBaseInfo toBeanIf() {
        this._xdb_verify_unsafe_();
        return this;
    }

    public Bean toConst() {
        this._xdb_verify_unsafe_();
        return new Const();
    }

    public long getRoleid() {
        this._xdb_verify_unsafe_();
        return this.roleid;
    }

    public String getRedpackid() {
        this._xdb_verify_unsafe_();
        return this.redpackid;
    }

    public Octets getRedpackidOctets() {
        this._xdb_verify_unsafe_();
        return Octets.wrap(this.getRedpackid(), "UTF-16LE");
    }

    public long getSendtime() {
        this._xdb_verify_unsafe_();
        return this.sendtime;
    }

    public void setRoleid(long _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "roleid") {
            protected Log create() {
                return new LogLong(this, RedPackBaseInfo.this.roleid) {
                    public void rollback() {
                        RedPackBaseInfo.this.roleid = this._xdb_saved;
                    }
                };
            }
        });
        this.roleid = _v_;
    }

    public void setRedpackid(String _v_) {
        this._xdb_verify_unsafe_();
        if (null == _v_) {
            throw new NullPointerException();
        } else {
            Logs.logIf(new LogKey(this, "redpackid") {
                protected Log create() {
                    return new LogString(this, RedPackBaseInfo.this.redpackid) {
                        public void rollback() {
                            RedPackBaseInfo.this.redpackid = this._xdb_saved;
                        }
                    };
                }
            });
            this.redpackid = _v_;
        }
    }

    public void setRedpackidOctets(Octets _v_) {
        this._xdb_verify_unsafe_();
        this.setRedpackid(_v_.getString("UTF-16LE"));
    }

    public void setSendtime(long _v_) {
        this._xdb_verify_unsafe_();
        Logs.logIf(new LogKey(this, "sendtime") {
            protected Log create() {
                return new LogLong(this, RedPackBaseInfo.this.sendtime) {
                    public void rollback() {
                        RedPackBaseInfo.this.sendtime = this._xdb_saved;
                    }
                };
            }
        });
        this.sendtime = _v_;
    }

    public final boolean equals(Object _o1_) {
        this._xdb_verify_unsafe_();
        RedPackBaseInfo _o_ = null;
        if (_o1_ instanceof RedPackBaseInfo) {
            _o_ = (RedPackBaseInfo)_o1_;
        } else {
            if (!(_o1_ instanceof Const)) {
                return false;
            }

            _o_ = ((Const)_o1_).nThis();
        }

        if (this.roleid != _o_.roleid) {
            return false;
        } else if (!this.redpackid.equals(_o_.redpackid)) {
            return false;
        } else {
            return this.sendtime == _o_.sendtime;
        }
    }

    public final int hashCode() {
        this._xdb_verify_unsafe_();
        int _h_ = 0;
        _h_ = (int)((long)_h_ + this.roleid);
        _h_ += this.redpackid.hashCode();
        _h_ = (int)((long)_h_ + this.sendtime);
        return _h_;
    }

    public String toString() {
        this._xdb_verify_unsafe_();
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.roleid);
        _sb_.append(",");
        _sb_.append("'").append(this.redpackid).append("'");
        _sb_.append(",");
        _sb_.append(this.sendtime);
        _sb_.append(")");
        return _sb_.toString();
    }

    public Listenable newListenable() {
        ListenableBean lb = new ListenableBean();
        lb.add((new ListenableChanged()).setVarName("roleid"));
        lb.add((new ListenableChanged()).setVarName("redpackid"));
        lb.add((new ListenableChanged()).setVarName("sendtime"));
        return lb;
    }

    private class Const implements xbean.RedPackBaseInfo {
        private Const() {
        }

        RedPackBaseInfo nThis() {
            return RedPackBaseInfo.this;
        }

        public void _reset_unsafe_() {
            throw new UnsupportedOperationException();
        }

        public xbean.RedPackBaseInfo copy() {
            return RedPackBaseInfo.this.copy();
        }

        public xbean.RedPackBaseInfo toData() {
            return RedPackBaseInfo.this.toData();
        }

        public xbean.RedPackBaseInfo toBean() {
            return RedPackBaseInfo.this.toBean();
        }

        public xbean.RedPackBaseInfo toDataIf() {
            return RedPackBaseInfo.this.toDataIf();
        }

        public xbean.RedPackBaseInfo toBeanIf() {
            return RedPackBaseInfo.this.toBeanIf();
        }

        public long getRoleid() {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            return RedPackBaseInfo.this.roleid;
        }

        public String getRedpackid() {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            return RedPackBaseInfo.this.redpackid;
        }

        public Octets getRedpackidOctets() {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            return RedPackBaseInfo.this.getRedpackidOctets();
        }

        public long getSendtime() {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            return RedPackBaseInfo.this.sendtime;
        }

        public void setRoleid(long _v_) {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setRedpackid(String _v_) {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setRedpackidOctets(Octets _v_) {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public void setSendtime(long _v_) {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public Bean toConst() {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            return this;
        }

        public boolean isConst() {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            return true;
        }

        public boolean isData() {
            return RedPackBaseInfo.this.isData();
        }

        public OctetsStream marshal(OctetsStream _os_) {
            return RedPackBaseInfo.this.marshal(_os_);
        }

        public OctetsStream unmarshal(OctetsStream arg0) throws MarshalException {
            RedPackBaseInfo.this._xdb_verify_unsafe_();
            throw new UnsupportedOperationException();
        }

        public Bean xdbParent() {
            return RedPackBaseInfo.this.xdbParent();
        }

        public boolean xdbManaged() {
            return RedPackBaseInfo.this.xdbManaged();
        }

        public String xdbVarname() {
            return RedPackBaseInfo.this.xdbVarname();
        }

        public Long xdbObjId() {
            return RedPackBaseInfo.this.xdbObjId();
        }

        public boolean equals(Object obj) {
            return RedPackBaseInfo.this.equals(obj);
        }

        public int hashCode() {
            return RedPackBaseInfo.this.hashCode();
        }

        public String toString() {
            return RedPackBaseInfo.this.toString();
        }
    }

    public static final class Data implements xbean.RedPackBaseInfo {
        private long roleid;
        private String redpackid;
        private long sendtime;

        public void _reset_unsafe_() {
            throw new UnsupportedOperationException();
        }

        public Data() {
            this.redpackid = "";
        }

        Data(xbean.RedPackBaseInfo _o1_) {
            if (_o1_ instanceof RedPackBaseInfo) {
                this.assign((RedPackBaseInfo)_o1_);
            } else if (_o1_ instanceof Data) {
                this.assign((Data)_o1_);
            } else {
                if (!(_o1_ instanceof Const)) {
                    throw new UnsupportedOperationException();
                }

                this.assign(((Const)_o1_).nThis());
            }

        }

        private void assign(RedPackBaseInfo _o_) {
            this.roleid = _o_.roleid;
            this.redpackid = _o_.redpackid;
            this.sendtime = _o_.sendtime;
        }

        private void assign(Data _o_) {
            this.roleid = _o_.roleid;
            this.redpackid = _o_.redpackid;
            this.sendtime = _o_.sendtime;
        }

        public final OctetsStream marshal(OctetsStream _os_) {
            _os_.marshal(this.roleid);
            _os_.marshal(this.redpackid, "UTF-16LE");
            _os_.marshal(this.sendtime);
            return _os_;
        }

        public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
            this.roleid = _os_.unmarshal_long();
            this.redpackid = _os_.unmarshal_String("UTF-16LE");
            this.sendtime = _os_.unmarshal_long();
            return _os_;
        }

        public xbean.RedPackBaseInfo copy() {
            return new Data(this);
        }

        public xbean.RedPackBaseInfo toData() {
            return new Data(this);
        }

        public xbean.RedPackBaseInfo toBean() {
            return new RedPackBaseInfo(this, (XBean)null, (String)null);
        }

        public xbean.RedPackBaseInfo toDataIf() {
            return this;
        }

        public xbean.RedPackBaseInfo toBeanIf() {
            return new RedPackBaseInfo(this, (XBean)null, (String)null);
        }

        public boolean xdbManaged() {
            throw new UnsupportedOperationException();
        }

        public Bean xdbParent() {
            throw new UnsupportedOperationException();
        }

        public String xdbVarname() {
            throw new UnsupportedOperationException();
        }

        public Long xdbObjId() {
            throw new UnsupportedOperationException();
        }

        public Bean toConst() {
            throw new UnsupportedOperationException();
        }

        public boolean isConst() {
            return false;
        }

        public boolean isData() {
            return true;
        }

        public long getRoleid() {
            return this.roleid;
        }

        public String getRedpackid() {
            return this.redpackid;
        }

        public Octets getRedpackidOctets() {
            return Octets.wrap(this.getRedpackid(), "UTF-16LE");
        }

        public long getSendtime() {
            return this.sendtime;
        }

        public void setRoleid(long _v_) {
            this.roleid = _v_;
        }

        public void setRedpackid(String _v_) {
            if (null == _v_) {
                throw new NullPointerException();
            } else {
                this.redpackid = _v_;
            }
        }

        public void setRedpackidOctets(Octets _v_) {
            this.setRedpackid(_v_.getString("UTF-16LE"));
        }

        public void setSendtime(long _v_) {
            this.sendtime = _v_;
        }

        public final boolean equals(Object _o1_) {
            if (!(_o1_ instanceof Data)) {
                return false;
            } else {
                Data _o_ = (Data)_o1_;
                if (this.roleid != _o_.roleid) {
                    return false;
                } else if (!this.redpackid.equals(_o_.redpackid)) {
                    return false;
                } else {
                    return this.sendtime == _o_.sendtime;
                }
            }
        }

        public final int hashCode() {
            int _h_ = 0;
            _h_ = (int)((long)_h_ + this.roleid);
            _h_ += this.redpackid.hashCode();
            _h_ = (int)((long)_h_ + this.sendtime);
            return _h_;
        }

        public String toString() {
            StringBuilder _sb_ = new StringBuilder();
            _sb_.append("(");
            _sb_.append(this.roleid);
            _sb_.append(",");
            _sb_.append("'").append(this.redpackid).append("'");
            _sb_.append(",");
            _sb_.append(this.sendtime);
            _sb_.append(")");
            return _sb_.toString();
        }
    }
}
