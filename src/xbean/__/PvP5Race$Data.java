//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import java.util.LinkedList;
import java.util.List;
import mkdb.Bean;
import mkdb.XBean;
import xbean.Pod;
import xbean.PvP5QueueRole;
import xbean.PvP5Race;
import xbean.PvP5RaceRole;
import com.locojoy.base.Octets;

public final class PvP5Race$Data implements PvP5Race {
    private LinkedList<PvP5RaceRole> allrolescampa;
    private LinkedList<PvP5RaceRole> allrolescampb;
    private LinkedList<Long> allrolesidcampa;
    private LinkedList<Long> allrolesidcampb;
    private LinkedList<PvP5QueueRole> waitingqueue;
    private int isend;

    public void _reset_unsafe_() {
        throw new UnsupportedOperationException();
    }

    public PvP5Race$Data() {
        this.allrolescampa = new LinkedList();
        this.allrolescampb = new LinkedList();
        this.allrolesidcampa = new LinkedList();
        this.allrolesidcampb = new LinkedList();
        this.waitingqueue = new LinkedList();
    }

    PvP5Race$Data(PvP5Race _o1_) {
        if (_o1_ instanceof xbean.__.PvP5Race) {
            this.assign((xbean.__.PvP5Race)_o1_);
        } else if (_o1_ instanceof PvP5Race$Data) {
            this.assign((PvP5Race$Data)_o1_);
        } else {
            if (!(_o1_ instanceof xbean.__.PvP5Race.Const)) {
                throw new UnsupportedOperationException();
            }

            this.assign(((xbean.__.PvP5Race.Const)_o1_).nThis());
        }

    }

    private void assign(xbean.__.PvP5Race _o_) {
        this.allrolescampa = new LinkedList();

        for(PvP5RaceRole _v_ : xbean.__.PvP5Race.access$800(_o_)) {
            this.allrolescampa.add(new xbean.__.PvP5RaceRole.Data(_v_));
        }

        this.allrolescampb = new LinkedList();

        for(PvP5RaceRole _v_ : xbean.__.PvP5Race.access$900(_o_)) {
            this.allrolescampb.add(new xbean.__.PvP5RaceRole.Data(_v_));
        }

        this.allrolesidcampa = new LinkedList();
        this.allrolesidcampa.addAll(xbean.__.PvP5Race.access$1000(_o_));
        this.allrolesidcampb = new LinkedList();
        this.allrolesidcampb.addAll(xbean.__.PvP5Race.access$1100(_o_));
        this.waitingqueue = new LinkedList();

        for(PvP5QueueRole _v_ : xbean.__.PvP5Race.access$1200(_o_)) {
            this.waitingqueue.add(new xbean.__.PvP5QueueRole.Data(_v_));
        }

        this.isend = xbean.__.PvP5Race.access$700(_o_);
    }

    private void assign(PvP5Race$Data _o_) {
        this.allrolescampa = new LinkedList();

        for(PvP5RaceRole _v_ : _o_.allrolescampa) {
            this.allrolescampa.add(new xbean.__.PvP5RaceRole.Data(_v_));
        }

        this.allrolescampb = new LinkedList();

        for(PvP5RaceRole _v_ : _o_.allrolescampb) {
            this.allrolescampb.add(new xbean.__.PvP5RaceRole.Data(_v_));
        }

        this.allrolesidcampa = new LinkedList();
        this.allrolesidcampa.addAll(_o_.allrolesidcampa);
        this.allrolesidcampb = new LinkedList();
        this.allrolesidcampb.addAll(_o_.allrolesidcampb);
        this.waitingqueue = new LinkedList();

        for(PvP5QueueRole _v_ : _o_.waitingqueue) {
            this.waitingqueue.add(new xbean.__.PvP5QueueRole.Data(_v_));
        }

        this.isend = _o_.isend;
    }

    public final OctetsStream marshal(OctetsStream _os_) {
        _os_.compact_uint32(this.allrolescampa.size());

        for(PvP5RaceRole _v_ : this.allrolescampa) {
            _v_.marshal(_os_);
        }

        _os_.compact_uint32(this.allrolescampb.size());

        for(PvP5RaceRole _v_ : this.allrolescampb) {
            _v_.marshal(_os_);
        }

        _os_.compact_uint32(this.allrolesidcampa.size());

        for(Long _v_ : this.allrolesidcampa) {
            _os_.marshal(_v_);
        }

        _os_.compact_uint32(this.allrolesidcampb.size());

        for(Long _v_ : this.allrolesidcampb) {
            _os_.marshal(_v_);
        }

        _os_.compact_uint32(this.waitingqueue.size());

        for(PvP5QueueRole _v_ : this.waitingqueue) {
            _v_.marshal(_os_);
        }

        _os_.marshal(this.isend);
        return _os_;
    }

    public final OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            PvP5RaceRole _v_ = Pod.newPvP5RaceRoleData();
            _v_.unmarshal(_os_);
            this.allrolescampa.add(_v_);
        }

        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            PvP5RaceRole _v_ = Pod.newPvP5RaceRoleData();
            _v_.unmarshal(_os_);
            this.allrolescampb.add(_v_);
        }

        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            long _v_ = 0L;
            _v_ = _os_.unmarshal_long();
            this.allrolesidcampa.add(_v_);
        }

        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            long _v_ = 0L;
            _v_ = _os_.unmarshal_long();
            this.allrolesidcampb.add(_v_);
        }

        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            PvP5QueueRole _v_ = Pod.newPvP5QueueRoleData();
            _v_.unmarshal(_os_);
            this.waitingqueue.add(_v_);
        }

        this.isend = _os_.unmarshal_int();
        return _os_;
    }

    public PvP5Race copy() {
        return new PvP5Race$Data(this);
    }

    public PvP5Race toData() {
        return new PvP5Race$Data(this);
    }

    public PvP5Race toBean() {
        return new xbean.__.PvP5Race(this, (XBean)null, (String)null);
    }

    public PvP5Race toDataIf() {
        return this;
    }

    public PvP5Race toBeanIf() {
        return new xbean.__.PvP5Race(this, (XBean)null, (String)null);
    }

    public boolean xdbManaged() {
        throw new UnsupportedOperationException();
    }

    public Bean xdbParent() {
        throw new UnsupportedOperationException();
    }

    public String xdbVarname() {
        throw new UnsupportedOperationException();
    }

    public Long xdbObjId() {
        throw new UnsupportedOperationException();
    }

    public Bean toConst() {
        throw new UnsupportedOperationException();
    }

    public boolean isConst() {
        return false;
    }

    public boolean isData() {
        return true;
    }

    public List<PvP5RaceRole> getAllrolescampa() {
        return this.allrolescampa;
    }

    public List<PvP5RaceRole> getAllrolescampaAsData() {
        return this.allrolescampa;
    }

    public List<PvP5RaceRole> getAllrolescampb() {
        return this.allrolescampb;
    }

    public List<PvP5RaceRole> getAllrolescampbAsData() {
        return this.allrolescampb;
    }

    public List<Long> getAllrolesidcampa() {
        return this.allrolesidcampa;
    }

    public List<Long> getAllrolesidcampaAsData() {
        return this.allrolesidcampa;
    }

    public List<Long> getAllrolesidcampb() {
        return this.allrolesidcampb;
    }

    public List<Long> getAllrolesidcampbAsData() {
        return this.allrolesidcampb;
    }

    public List<PvP5QueueRole> getWaitingqueue() {
        return this.waitingqueue;
    }

    public List<PvP5QueueRole> getWaitingqueueAsData() {
        return this.waitingqueue;
    }

    public int getIsend() {
        return this.isend;
    }

    public void setIsend(int _v_) {
        this.isend = _v_;
    }

    public final boolean equals(Object _o1_) {
        if (!(_o1_ instanceof PvP5Race$Data)) {
            return false;
        } else {
            PvP5Race$Data _o_ = (PvP5Race$Data)_o1_;
            if (!this.allrolescampa.equals(_o_.allrolescampa)) {
                return false;
            } else if (!this.allrolescampb.equals(_o_.allrolescampb)) {
                return false;
            } else if (!this.allrolesidcampa.equals(_o_.allrolesidcampa)) {
                return false;
            } else if (!this.allrolesidcampb.equals(_o_.allrolesidcampb)) {
                return false;
            } else if (!this.waitingqueue.equals(_o_.waitingqueue)) {
                return false;
            } else {
                return this.isend == _o_.isend;
            }
        }
    }

    public final int hashCode() {
        int _h_ = 0;
        _h_ += this.allrolescampa.hashCode();
        _h_ += this.allrolescampb.hashCode();
        _h_ += this.allrolesidcampa.hashCode();
        _h_ += this.allrolesidcampb.hashCode();
        _h_ += this.waitingqueue.hashCode();
        _h_ += this.isend;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.allrolescampa);
        _sb_.append(",");
        _sb_.append(this.allrolescampb);
        _sb_.append(",");
        _sb_.append(this.allrolesidcampa);
        _sb_.append(",");
        _sb_.append(this.allrolesidcampb);
        _sb_.append(",");
        _sb_.append(this.waitingqueue);
        _sb_.append(",");
        _sb_.append(this.isend);
        _sb_.append(")");
        return _sb_.toString();
    }
}
