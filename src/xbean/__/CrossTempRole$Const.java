//
//

package xbean.__;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import java.util.HashMap;
import java.util.Map;
import mkdb.Bean;
import mkdb.Consts;
import xbean.CrossTempRole;
import com.locojoy.base.Octets;
import java.util.Set;

class CrossTempRole$Const implements CrossTempRole {
    private CrossTempRole$Const(xbean.__.CrossTempRole var1) {
        this.this$0 = var1;
    }

    xbean.__.CrossTempRole nThis() {
        return this.this$0;
    }

    public void _reset_unsafe_() {
        throw new UnsupportedOperationException();
    }

    public CrossTempRole copy() {
        return this.this$0.copy();
    }

    public CrossTempRole toData() {
        return this.this$0.toData();
    }

    public CrossTempRole toBean() {
        return this.this$0.toBean();
    }

    public CrossTempRole toDataIf() {
        return this.this$0.toDataIf();
    }

    public CrossTempRole toBeanIf() {
        return this.this$0.toBeanIf();
    }

    public int getZoneid() {
        this.this$0._xdb_verify_unsafe_();
        return xbean.__.CrossTempRole.access$700(this.this$0);
    }

    public long getLastcopytime() {
        this.this$0._xdb_verify_unsafe_();
        return xbean.__.CrossTempRole.access$800(this.this$0);
    }

    public short getCopyresult() {
        this.this$0._xdb_verify_unsafe_();
        return xbean.__.CrossTempRole.access$900(this.this$0);
    }

    public Map<String, Boolean> getFinishedtable() {
        this.this$0._xdb_verify_unsafe_();
        return Consts.constMap(xbean.__.CrossTempRole.access$1200(this.this$0));
    }

    public Map<String, Boolean> getFinishedtableAsData() {
        this.this$0._xdb_verify_unsafe_();
        xbean.__.CrossTempRole _o_ = this.this$0;
        Map<String, Boolean> finishedtable = new HashMap();

        for(Map.Entry<String, Boolean> _e_ : xbean.__.CrossTempRole.access$1200(_o_).entrySet()) {
            finishedtable.put(_e_.getKey(), _e_.getValue());
        }

        return finishedtable;
    }

    public boolean getCrosstatus() {
        this.this$0._xdb_verify_unsafe_();
        return xbean.__.CrossTempRole.access$1000(this.this$0);
    }

    public int getFlag() {
        this.this$0._xdb_verify_unsafe_();
        return xbean.__.CrossTempRole.access$1100(this.this$0);
    }

    public void setZoneid(int _v_) {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public void setLastcopytime(long _v_) {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public void setCopyresult(short _v_) {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public void setCrosstatus(boolean _v_) {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public void setFlag(int _v_) {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public Bean toConst() {
        this.this$0._xdb_verify_unsafe_();
        return this;
    }

    public boolean isConst() {
        this.this$0._xdb_verify_unsafe_();
        return true;
    }

    public boolean isData() {
        return this.this$0.isData();
    }

    public OctetsStream marshal(OctetsStream _os_) {
        return this.this$0.marshal(_os_);
    }

    public OctetsStream unmarshal(OctetsStream arg0) throws MarshalException {
        this.this$0._xdb_verify_unsafe_();
        throw new UnsupportedOperationException();
    }

    public Bean xdbParent() {
        return this.this$0.xdbParent();
    }

    public boolean xdbManaged() {
        return this.this$0.xdbManaged();
    }

    public String xdbVarname() {
        return this.this$0.xdbVarname();
    }

    public Long xdbObjId() {
        return this.this$0.xdbObjId();
    }

    public boolean equals(Object obj) {
        return this.this$0.equals(obj);
    }

    public int hashCode() {
        return this.this$0.hashCode();
    }

    public String toString() {
        return this.this$0.toString();
    }
}
