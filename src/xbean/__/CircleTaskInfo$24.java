//
//

package xbean.__;

import mkdb.Log;
import mkdb.LogKey;
import mkdb.XBean;
import mkdb.Bean;

class CircleTaskInfo$24 extends LogKey {
    CircleTaskInfo$24(CircleTaskInfo this$0, XBean arg0, String arg1) {
        super(arg0, arg1);
        this.this$0 = this$0;
    }

    protected Log create() {
        return new CircleTaskInfo$24$1(this, this, CircleTaskInfo.access$5700(this.this$0));
    }
}
