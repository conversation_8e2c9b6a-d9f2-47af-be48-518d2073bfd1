//
//

package xbean;

import java.util.Map;
import mkdb.Bean;

public interface EnhancementData extends Bean {
    EnhancementData copy();

    EnhancementData toData();

    EnhancementData toBean();

    EnhancementData toDataIf();

    EnhancementData toBeanIf();

    Map<Integer, Integer> getEnhancementattr();

    Map<Integer, Integer> getEnhancementattrAsData();

    long getEnhancementtime();

    void setEnhancementtime(long value);
}
