//
//

package gnet;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import xbean.Properties;
import xbean.User;
import com.locojoy.base.Octets;

public class GetAddCashSN2Req extends __GetAddCashSN2Req__ {
    public static final int PROTOCOL_TYPE = 202;
    public int xid;
    public int userid;
    public int zoneid;
    public byte force;

    protected void process() {
        int sn = 0;
        boolean hasRole = false;
        User u = xtable.User.select(this.userid);
        GetAddCashSN2Rep getAddCashSNRep = new GetAddCashSN2Rep();
        long roleid = 0L;
        if (u != null) {
            for(Long rid : u.getIdlist()) {
                Properties prop = xtable.Properties.select(rid);
                if (prop != null && prop.getDeletetime() == 0L) {
                    hasRole = true;
                    break;
                }
            }

            roleid = u.getPrevloginroleid();
        }

        if (hasRole && roleid > 0L) {
            getAddCashSNRep.sn = sn + 1;
            getAddCashSNRep.userid = this.userid;
            getAddCashSNRep.xid = this.xid;
            getAddCashSNRep.zoneid = this.zoneid;
            getAddCashSNRep.retcode = 0;
        } else {
            getAddCashSNRep.sn = sn + 1;
            getAddCashSNRep.userid = this.userid;
            getAddCashSNRep.xid = this.xid;
            getAddCashSNRep.zoneid = this.zoneid;
            getAddCashSNRep.retcode = -19;
        }

        DeliveryManager.getInstance().send(getAddCashSNRep);
    }

    public int getType() {
        return 202;
    }

    public GetAddCashSN2Req() {
        this.xid = -1;
        this.userid = -1;
        this.zoneid = -1;
        this.force = -1;
    }

    public GetAddCashSN2Req(int _xid_, int _userid_, int _zoneid_, byte _force_) {
        this.xid = _xid_;
        this.userid = _userid_;
        this.zoneid = _zoneid_;
        this.force = _force_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.xid);
            _os_.marshal(this.userid);
            _os_.marshal(this.zoneid);
            _os_.marshal(this.force);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.xid = _os_.unmarshal_int();
        this.userid = _os_.unmarshal_int();
        this.zoneid = _os_.unmarshal_int();
        this.force = _os_.unmarshal_byte();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof GetAddCashSN2Req) {
            GetAddCashSN2Req _o_ = (GetAddCashSN2Req)_o1_;
            if (this.xid != _o_.xid) {
                return false;
            } else if (this.userid != _o_.userid) {
                return false;
            } else if (this.zoneid != _o_.zoneid) {
                return false;
            } else {
                return this.force == _o_.force;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.xid;
        _h_ += this.userid;
        _h_ += this.zoneid;
        _h_ += this.force;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.xid).append(",");
        _sb_.append(this.userid).append(",");
        _sb_.append(this.zoneid).append(",");
        _sb_.append(this.force).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(GetAddCashSN2Req _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = this.xid - _o_.xid;
            if (0 != _c_) {
                return _c_;
            } else {
                _c_ = this.userid - _o_.userid;
                if (0 != _c_) {
                    return _c_;
                } else {
                    _c_ = this.zoneid - _o_.zoneid;
                    if (0 != _c_) {
                        return _c_;
                    } else {
                        _c_ = this.force - _o_.force;
                        return 0 != _c_ ? _c_ : _c_;
                    }
                }
            }
        }
    }
}
