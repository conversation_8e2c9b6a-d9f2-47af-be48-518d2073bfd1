//
//

package fire.log.beans;

import java.util.ArrayList;
import java.util.List;
import mkdb.Bean;

public class MailBean {
    private byte kind;
    private long mailid;
    private String titile;
    private String content;
    private String time;
    private List<ItemBaseBean> items = new ArrayList();

    public MailBean(byte kind, long mailid, String titile, String content, String time, List<ItemBaseBean> items) {
        this.kind = kind;
        this.mailid = mailid;
        this.titile = titile;
        this.content = content;
        this.time = time;
        this.items.addAll(items);
    }

    public byte getKind() {
        return this.kind;
    }

    public void setKind(byte kind) {
        this.kind = kind;
    }

    public long getMailid() {
        return this.mailid;
    }

    public void setMailid(long mailid) {
        this.mailid = mailid;
    }

    public String getTitile() {
        return this.titile;
    }

    public void setTitile(String titile) {
        this.titile = titile;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTime() {
        return this.time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public List<ItemBaseBean> getItems() {
        return this.items;
    }

    public void setItems(List<ItemBaseBean> items) {
        this.items = items;
    }
}
