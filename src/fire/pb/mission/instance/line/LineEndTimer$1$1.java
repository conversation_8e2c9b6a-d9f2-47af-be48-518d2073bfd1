//
//

package fire.pb.mission.instance.line;

import fire.msp.IGetRolesCallBack;
import java.util.List;
import mkdb.Procedure;

class LineEndTimer$1$1 implements IGetRolesCallBack {
    LineEndTimer$1$1(LineEndTimer.1 this$1, Integer var2) {
        this.this$1 = this$1;
        this.val$mapId = var2;
    }

    public void process(List<Long> roleIds) {
        Procedure.pexecuteWhileCommit(new LineEndTimer$1$1$1(this, roleIds));
    }
}
