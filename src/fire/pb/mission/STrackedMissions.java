//
//

package fire.pb.mission;

import com.locojoy.base.Marshal.Marshal;
import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import java.util.HashMap;
import java.util.Map;
import com.locojoy.base.Octets;
import java.util.Set;

public class STrackedMissions extends __STrackedMissions__ {
    public static final int PROTOCOL_TYPE = 805461;
    public HashMap<Integer, TrackedMission> trackedmissions;

    protected void process() {
    }

    public int getType() {
        return 805461;
    }

    public STrackedMissions() {
        this.trackedmissions = new HashMap();
    }

    public STrackedMissions(HashMap<Integer, TrackedMission> _trackedmissions_) {
        this.trackedmissions = _trackedmissions_;
    }

    public final boolean _validator_() {
        for(Map.Entry<Integer, TrackedMission> _e_ : this.trackedmissions.entrySet()) {
            if (!((TrackedMission)_e_.getValue())._validator_()) {
                return false;
            }
        }

        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.compact_uint32(this.trackedmissions.size());

            for(Map.Entry<Integer, TrackedMission> _e_ : this.trackedmissions.entrySet()) {
                _os_.marshal((Integer)_e_.getKey());
                _os_.marshal((Marshal)_e_.getValue());
            }

            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        for(int size = _os_.uncompact_uint32(); size > 0; --size) {
            int _k_ = _os_.unmarshal_int();
            TrackedMission _v_ = new TrackedMission();
            _v_.unmarshal(_os_);
            this.trackedmissions.put(_k_, _v_);
        }

        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof STrackedMissions) {
            STrackedMissions _o_ = (STrackedMissions)_o1_;
            return this.trackedmissions.equals(_o_.trackedmissions);
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.trackedmissions.hashCode();
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.trackedmissions).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }
}
