//
//

package fire.pb.mission;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Octets;

public class SCopyDestroyTime extends __SCopyDestroyTime__ {
    public static final int PROTOCOL_TYPE = 805471;
    public long destroytime;

    protected void process() {
    }

    public int getType() {
        return 805471;
    }

    public SCopyDestroyTime() {
    }

    public SCopyDestroyTime(long _destroytime_) {
        this.destroytime = _destroytime_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.destroytime);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.destroytime = _os_.unmarshal_long();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof SCopyDestroyTime) {
            SCopyDestroyTime _o_ = (SCopyDestroyTime)_o1_;
            return this.destroytime == _o_.destroytime;
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += (int)this.destroytime;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.destroytime).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(SCopyDestroyTime _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = Long.signum(this.destroytime - _o_.destroytime);
            return 0 != _c_ ? _c_ : _c_;
        }
    }
}
