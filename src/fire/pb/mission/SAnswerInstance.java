//
//

package fire.pb.mission;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Octets;

public class SAnswerInstance extends __SAnswerInstance__ {
    public static final int PROTOCOL_TYPE = 805546;
    public long roleid;
    public short answer;

    protected void process() {
    }

    public int getType() {
        return 805546;
    }

    public SAnswerInstance() {
    }

    public SAnswerInstance(long _roleid_, short _answer_) {
        this.roleid = _roleid_;
        this.answer = _answer_;
    }

    public final boolean _validator_() {
        return this.roleid >= 0L;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.roleid);
            _os_.marshal(this.answer);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.roleid = _os_.unmarshal_long();
        this.answer = _os_.unmarshal_short();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof SAnswerInstance) {
            SAnswerInstance _o_ = (SAnswerInstance)_o1_;
            if (this.roleid != _o_.roleid) {
                return false;
            } else {
                return this.answer == _o_.answer;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += (int)this.roleid;
        _h_ += this.answer;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.roleid).append(",");
        _sb_.append(this.answer).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(SAnswerInstance _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = Long.signum(this.roleid - _o_.roleid);
            if (0 != _c_) {
                return _c_;
            } else {
                _c_ = this.answer - _o_.answer;
                return 0 != _c_ ? _c_ : _c_;
            }
        }
    }
}
