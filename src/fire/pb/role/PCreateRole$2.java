//
//

package fire.pb.role;

import com.locojoy.base.Runnable;
import fire.pb.SCreateRole;

class PCreateRole$2 extends Runnable {
    PCreateRole$2(PCreateRole this$0, SCreateRole var2) {
        this.this$0 = this$0;
        this.val$snd = var2;
    }

    public void run() {
        if (!PCreateRole.access$000(this.this$0, this.val$snd.newinfo.roleid, this.val$snd.newinfo.rolename, this.val$snd.newinfo.shape, this.val$snd.newinfo.level)) {
            PCreateRole.access$100().error("PCreateRole.InsertMysqlRelation failed!");
        }

    }
}
