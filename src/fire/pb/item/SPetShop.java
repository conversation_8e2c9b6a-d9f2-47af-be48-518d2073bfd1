package fire.pb.item;

import mytools.ConvMain;

/**
 * SPetShop - 自动生成的配置类
 * Auto-generated configuration class for SPetShop
 */
public class SPetShop implements ConvMain.Checkable, Comparable<SPetShop> {
    
    // 主键ID
    public int id = 0;
    
    // 名称
    public String name = "";
    
    // 默认构造函数
    public SPetShop() {
    }
    
    // 拷贝构造函数
    public SPetShop(SPetShop other) {
        this.id = other.id;
        this.name = other.name;
    }
    
    @Override
    public int compareTo(SPetShop other) {
        return Integer.compare(this.id, other.id);
    }
    
    @Override
    public void checkValid(java.util.Map<String, java.util.Map<Integer, ? extends Object>> objs) {
        // 验证逻辑可以在这里实现
    }
    
    // Getter和Setter方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public String toString() {
        return "SPetShop{" +
                "id=" + id +
                ", name='" + name + "\'" +
                "}";
    }
    
    // 内部异常类
    static class NeedId extends RuntimeException {
        private static final long serialVersionUID = 1L;
    }
}