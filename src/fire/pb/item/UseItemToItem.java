//
//

package fire.pb.item;

import fire.pb.item.Commontext.UseResult;

public class UseItemToItem implements UseItemHandler {
    private final int packid;
    private final int keyinpack;

    public UseItemToItem(int packid, int keyinpack) {
        this.packid = packid;
        this.keyinpack = keyinpack;
    }

    public Commontext.UseResult onUse(long roleId, ItemBase bi, int usednum) {
        return usednum != 1 ? UseResult.FAIL : bi.appendToItem(this.packid, this.keyinpack, usednum);
    }
}
