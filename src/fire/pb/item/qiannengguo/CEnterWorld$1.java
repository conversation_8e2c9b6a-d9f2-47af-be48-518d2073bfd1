//
//

package fire.pb.item.qiannengguo;

import mkdb.Procedure;
import xbean.ItemUse;
import xbean.ItemUseCount;
import xbean.Pod;
import xtable.Roleuseitemcount;

class CEnterWorld$1 extends Procedure {
    CEnterWorld$1(CEnterWorld var1, long var2) {
        this.this$0 = var1;
        this.val$roleId = var2;
    }

    protected boolean process() {
        ItemUse var1 = Roleuseitemcount.get(this.val$roleId);
        if (var1 == null) {
            var1 = Pod.newItemUse();
            Roleuseitemcount.insert(this.val$roleId, var1);
        }

        ItemUseCount var2 = (ItemUseCount)var1.getIteminfo().get(400156);
        if (var2 == null) {
            var2 = Pod.newItemUseCount();
            var1.getIteminfo().put(400156, var2);
        }

        int var3 = var2.getUsetimes();
        Procedure.psendWhileCommit(this.val$roleId, new SEnterWorld(new ReturnData(var3)));
        return true;
    }
}
