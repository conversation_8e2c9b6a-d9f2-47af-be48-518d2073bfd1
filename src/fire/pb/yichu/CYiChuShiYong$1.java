//
//

package fire.pb.yichu;

import fire.msp.task.GChangeShape;
import fire.pb.GsClient;
import fire.pb.buff.BuffAgent;
import fire.pb.buff.BuffRoleImpl;
import fire.pb.item.SShiZhuangYiChu;
import fire.pb.main.ConfigManager;
import fire.pb.skill.Result;
import fire.pb.skill.SkillRole;
import fire.pb.talk.MessageMgr;
import java.util.List;
import java.util.Map;
import mkdb.Procedure;
import xbean.BattleInfo;
import xbean.Properties;
import java.util.Set;

class CYiChuShiYong$1 extends Procedure {
    CYiChuShiYong$1(CYiChuShiYong this$0, long var2, Map var4) {
        this.this$0 = this$0;
        this.val$roleId = var2;
        this.val$sRoleRColorConfig = var4;
    }

    protected boolean process() {
        BuffAgent buffRole = new BuffRoleImpl(this.val$roleId, false);

        for(Map.Entry<Integer, SShiZhuangYiChu> integerSShiZhuangYiChuEntry : this.val$sRoleRColorConfig.entrySet()) {
            if (((SShiZhuangYiChu)integerSShiZhuangYiChuEntry.getValue()).moxing == this.this$0.moxing) {
                buffRole.removeCBuff(((SShiZhuangYiChu)integerSShiZhuangYiChuEntry.getValue()).buff);
                SkillRole spet = new SkillRole(this.val$roleId);
                Result result = spet.addSkillBuffWhileOnline((BattleInfo)null);
                buffRole.psendSBuffChangeResult(result);
            }
        }

        Properties pro = xtable.Properties.get(this.val$roleId);
        SShiZhuangYiChu sShiZhuangYiChu = (SShiZhuangYiChu)ConfigManager.getInstance().getConf(SShiZhuangYiChu.class).get(pro.getshizhuang().get(this.this$0.shizhuangid));
        pro.setShape(sShiZhuangYiChu.moxing);
        GChangeShape send2Scene = new GChangeShape();
        send2Scene.playerid = this.val$roleId;
        send2Scene.changetype = 0;
        send2Scene.shape = pro.getShape();
        GsClient.pSendWhileCommit(send2Scene);
        SChangeYiChu sshizhuang = new SChangeYiChu();
        sshizhuang.shape = pro.getShape();
        Procedure.psendWhileCommit(this.val$roleId, sshizhuang);
        MessageMgr.psendMsgNotify(this.val$roleId, 201057, (List)null);
        MessageMgr.psendSystemMessageToRole(this.val$roleId, 201057, (List)null);
        buffRole.addCBuffWithSP(sShiZhuangYiChu.buff);
        return true;
    }
}
