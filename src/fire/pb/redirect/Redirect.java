//
//

package fire.pb.redirect;

import fire.pb.main.ConfigManager;
import fire.pb.map.MapConfig;
import fire.pb.scene.Scene;
import fire.pb.scene.movable.Role;
import fire.pb.scene.sPos.GridPos;
import fire.pb.scene.sPos.Position;
import java.util.Map;

public class Redirect {
    public static void getoutFromDynamicScene(Role role, Scene curScene, int gototype) {
        if (role != null) {
            RedirectInfo info = getRedirectInfo(role, curScene);
            if (info != null) {
                role.justGoto((long)info.getMapID(), info.getGPosx(), info.getGPosy(), 0, gototype);
            }
        }
    }

    public static RedirectInfo getRedirectInfo(Role role, Scene curScene) {
        int mapId = -1;
        int gposx = -1;
        int gposy = -1;
        int gposz = -1;
        if (role.getLastStaticMapInfo() != null && role.getLastStaticMapInfo().sceneid > 0L) {
            mapId = (int)role.getLastStaticMapInfo().sceneid;
            GridPos roleGrid = (new Position(role.getLastStaticMapInfo().posx, role.getLastStaticMapInfo().posy, role.getLastStaticMapInfo().posz)).toGridPos();
            gposx = roleGrid.getX();
            gposy = roleGrid.getY();
            gposz = roleGrid.getZ();
        }

        if (mapId == -1 && curScene != null) {
            MapConfig conf = curScene.getMapConfig();
            if (conf.dynamic == 1) {
                mapId = conf.getRemap();
                gposx = conf.getRexjPos();
                gposy = conf.getReyjPos();
                gposz = 0;
            }
        }

        if (mapId == -1) {
            fire.pb.role.Redirect config = (fire.pb.role.Redirect)ConfigManager.getInstance().getConf(fire.pb.role.Redirect.class).get(role.getSchoolId());
            if (config != null) {
                mapId = config.remapid;
                gposx = config.reposx;
                gposy = config.reposy;
                gposz = 0;
            }
        }

        if (mapId != -1) {
            return new RedirectInfo(mapId, gposx, gposy, gposz);
        } else {
            Scene.LOG.info("进行场景重定向时找不到目标场景");
            return null;
        }
    }

    public static RedirectInfo getRedirectInfo(int school) {
        fire.pb.role.Redirect config = (fire.pb.role.Redirect)ConfigManager.getInstance().getConf(fire.pb.role.Redirect.class).get(school);
        return null != config ? new RedirectInfo(config.remapid, config.reposx, config.reposy, 0) : null;
    }
}
