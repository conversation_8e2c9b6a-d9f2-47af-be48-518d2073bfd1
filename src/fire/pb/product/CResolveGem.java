//
//

package fire.pb.product;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import fire.pb.item.PResolveGem;
import gnet.link.Onlines;
import com.locojoy.base.Octets;

public class CResolveGem extends __CResolveGem__ {
    public static final int PROTOCOL_TYPE = 803453;
    public int itemkey;

    protected void process() {
        long roleid = Onlines.getInstance().findRoleid(this);
        if (roleid >= 0L) {
            (new PResolveGem(roleid, this.itemkey)).submit();
        }
    }

    public int getType() {
        return 803453;
    }

    public CResolveGem() {
    }

    public CResolveGem(int _itemkey_) {
        this.itemkey = _itemkey_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.itemkey);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.itemkey = _os_.unmarshal_int();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof CResolveGem) {
            CResolveGem _o_ = (CResolveGem)_o1_;
            return this.itemkey == _o_.itemkey;
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.itemkey;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.itemkey).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(CResolveGem _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = this.itemkey - _o_.itemkey;
            return 0 != _c_ ? _c_ : _c_;
        }
    }
}
