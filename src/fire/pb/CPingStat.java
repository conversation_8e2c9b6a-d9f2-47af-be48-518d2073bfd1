//
//

package fire.pb;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import fire.pb.main.ConfigManager;
import fire.pb.util.InetAddressUtil;
import gnet.link.Onlines;
import gnet.link.Role;
import java.util.HashMap;
import java.util.Map;
import xbean.AUUserInfo;
import xtable.Auuserinfo;
import xtable.Properties;
import com.locojoy.base.Octets;

public class CPingStat extends __CPingStat__ {
    public static final int PROTOCOL_TYPE = 786490;
    public long beginstamp;
    public PingStatEntry pingstats;
    public short losspercent;

    protected void process() {
        Role role = Onlines.getInstance().find(this);
        if (role != null) {
            AUUserInfo auUserInfo = Auuserinfo.select(role.getUserid());
            Map<String, Object> params = new HashMap();
            params.put("from", ConfigManager.getGsZoneId());
            params.put("accountid", role.getUserid());
            params.put("account", auUserInfo != null ? auUserInfo.getUsername() : "unknown");
            params.put("roleid", role.getRoleid());
            params.put("name", Properties.selectRolename(role.getRoleid()));
            params.put("peer", InetAddressUtil.ipInt2String(auUserInfo != null ? auUserInfo.getLoginip() : 0));
            params.put("maxtime", this.pingstats._max);
            params.put("mintime", this.pingstats._min);
            params.put("avgtime", this.pingstats._avg);
            params.put("lossrate", this.losspercent);
        }
    }

    public int getType() {
        return 786490;
    }

    public CPingStat() {
        this.pingstats = new PingStatEntry();
    }

    public CPingStat(long _beginstamp_, PingStatEntry _pingstats_, short _losspercent_) {
        this.beginstamp = _beginstamp_;
        this.pingstats = _pingstats_;
        this.losspercent = _losspercent_;
    }

    public final boolean _validator_() {
        return this.pingstats._validator_();
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.beginstamp);
            _os_.marshal(this.pingstats);
            _os_.marshal(this.losspercent);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.beginstamp = _os_.unmarshal_long();
        this.pingstats.unmarshal(_os_);
        this.losspercent = _os_.unmarshal_short();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof CPingStat) {
            CPingStat _o_ = (CPingStat)_o1_;
            if (this.beginstamp != _o_.beginstamp) {
                return false;
            } else if (!this.pingstats.equals(_o_.pingstats)) {
                return false;
            } else {
                return this.losspercent == _o_.losspercent;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += (int)this.beginstamp;
        _h_ += this.pingstats.hashCode();
        _h_ += this.losspercent;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.beginstamp).append(",");
        _sb_.append(this.pingstats).append(",");
        _sb_.append(this.losspercent).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(CPingStat _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = Long.signum(this.beginstamp - _o_.beginstamp);
            if (0 != _c_) {
                return _c_;
            } else {
                _c_ = this.pingstats.compareTo(_o_.pingstats);
                if (0 != _c_) {
                    return _c_;
                } else {
                    _c_ = this.losspercent - _o_.losspercent;
                    return 0 != _c_ ? _c_ : _c_;
                }
            }
        }
    }
}
