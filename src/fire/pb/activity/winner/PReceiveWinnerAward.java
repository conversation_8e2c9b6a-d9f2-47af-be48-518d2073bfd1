//
//

package fire.pb.activity.winner;

import fire.log.enums.YYLoggerTuJingEnum;
import fire.pb.item.ItemMaps;
import fire.pb.item.Module;
import fire.pb.item.Pack;
import mkdb.Procedure;
import java.util.List;
import java.util.Map;

public class PReceiveWinnerAward extends Procedure {
    public static final int ONE_AWARD = 1;
    public static final int TOW_AWARD = 2;
    public static final int THREE_AWARD = 3;
    private final long roleid;
    private final int awardType;

    public PReceiveWinnerAward(long var1, int var3) {
        this.roleid = var1;
        this.awardType = var3;
    }

    protected boolean process() throws Exception {
        int var1 = 0;
        if (this.awardType == 1) {
            var1 = WinnerManager.getAwardListOne();
        } else if (this.awardType == 2) {
            var1 = WinnerManager.getAwardListTow();
        } else {
            var1 = WinnerManager.getAwardListThree();
        }

        WinnerRoleRecord var2 = (WinnerRoleRecord)WinnerManager.winnerrolerecords.get(this.roleid);
        if (var2 == null) {
            return false;
        } else {
            WinnerManager.logger.info("角色id " + this.roleid + "\t领取冠军试炼奖励，名次" + this.awardType);
            if (var2.getAwardflag() == 1) {
                return false;
            } else {
                Pack var3 = (Pack)Module.getInstance().getItemMaps(this.roleid, 1, false);
                if (var3.isFull()) {
                    ItemMaps var4 = Module.getInstance().getItemMaps(this.roleid, 4, false);
                    if (var4.doAddItem(var1, 1, "冠军试炼奖励", YYLoggerTuJingEnum.tujing_Value_guanjunshilianget, 2) != 1) {
                        WinnerManager.logger.info("玩家roleid " + this.roleid + "冠军试炼奖励出错2");
                    }
                } else if (var3.doAddItem(var1, 1, "冠军试炼奖励", YYLoggerTuJingEnum.tujing_Value_guanjunshilianget, 2) != 1) {
                    WinnerManager.logger.info("玩家roleid " + this.roleid + "冠军试炼奖励出错2");
                }

                WinnerManager.logger.info("角色id " + this.roleid + "\t领取冠军试炼奖励，名次" + this.awardType + "\t奖励领取成功");
                return true;
            }
        }
    }
}
