//
//

package fire.pb.activity.winner;

import fire.pb.util.TaskDlgUtil;
import mkdb.Procedure;

class WinnerManager$4 extends Procedure {
    WinnerManager$4(WinnerManager this$0, long var2) {
        this.this$0 = this$0;
        this.val$roleid = var2;
    }

    protected boolean process() throws Exception {
        TaskDlgUtil.refreshTaskState(this.val$roleid, 701002, 5);
        TaskDlgUtil.refreshTaskState(this.val$roleid, 701001, 5);
        return true;
    }
}
