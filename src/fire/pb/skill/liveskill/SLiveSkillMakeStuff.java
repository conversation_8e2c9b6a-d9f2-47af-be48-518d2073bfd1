//
//

package fire.pb.skill.liveskill;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Octets;

public class SLiveSkillMakeStuff extends __SLiveSkillMakeStuff__ {
    public static final int PROTOCOL_TYPE = 800518;
    public int ret;

    protected void process() {
    }

    public int getType() {
        return 800518;
    }

    public SLiveSkillMakeStuff() {
    }

    public SLiveSkillMakeStuff(int _ret_) {
        this.ret = _ret_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.ret);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.ret = _os_.unmarshal_int();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof SLiveSkillMakeStuff) {
            SLiveSkillMakeStuff _o_ = (SLiveSkillMakeStuff)_o1_;
            return this.ret == _o_.ret;
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.ret;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.ret).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(SLiveSkillMakeStuff _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = this.ret - _o_.ret;
            return 0 != _c_ ? _c_ : _c_;
        }
    }
}
