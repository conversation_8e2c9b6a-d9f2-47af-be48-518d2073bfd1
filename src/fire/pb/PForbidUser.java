//
//

package fire.pb;

import fire.log.LogManager;
import fire.log.Logger;
import gnet.link.Onlines;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import mkdb.Executor;
import mkdb.Procedure;
import xbean.Pod;
import xbean.PunishRecord;
import xbean.User;
import xbean.UserPunish;
import xtable.Userpunish;
import java.util.HashMap;

public class PForbidUser extends Procedure {
    private static Map<Integer, Integer> extraProbMap = new ConcurrentHashMap();
    private final int dstuserid;
    private final int gmuserid;
    private final int gmlocalsid;
    private int forbid_time;
    private String reason;
    private int kickoutReason;
    private final boolean auForbid;
    static Logger logger = Logger.getLogger("SYSTEM");

    public PForbidUser(int dstuserid, int gmuserid, int gmlocalsid, int forbid_time, String reason, boolean auForbid, int kickoutReason) {
        this.dstuserid = dstuserid;
        this.gmlocalsid = gmlocalsid;
        this.gmuserid = gmuserid;
        this.forbid_time = forbid_time;
        this.reason = reason;
        this.kickoutReason = kickoutReason;
        this.auForbid = auForbid;
    }

    protected boolean process() throws Exception {
        User user = xtable.User.select(this.dstuserid);
        if (user == null) {
            logger.error("forbid failed.dstuserid didn't exist.dstuserid:" + this.dstuserid);
            return false;
        } else {
            final long roleid = user.getPrevloginroleid();
            UserPunish userpunish = Userpunish.get(this.dstuserid);
            if (userpunish == null) {
                userpunish = Pod.newUserPunish();
                Userpunish.insert(this.dstuserid, userpunish);
            }

            if (this.forbid_time == -5) {
                Integer value = (Integer)extraProbMap.get(this.dstuserid);
                if (value == null) {
                    extraProbMap.put(this.dstuserid, 1);
                } else {
                    extraProbMap.put(this.dstuserid, value + 1);
                }

                return true;
            } else {
                this.kickoutReason = 2055;
                if (this.forbid_time == -1) {
                    this.kickoutReason = 2056;
                }

                if (this.forbid_time == -2) {
                    userpunish.setWaiguatimes(userpunish.getWaiguatimes() + 1);
                    userpunish.setSendmsgtime(0L);
                    this.kickoutReason = 2053;
                    if (userpunish.getWaiguatimes() == 1) {
                        this.forbid_time = 10;
                    } else if (userpunish.getWaiguatimes() == 2) {
                        this.forbid_time = 1440;
                    } else if (userpunish.getWaiguatimes() == 3) {
                        this.forbid_time = 10080;
                    } else {
                        this.forbid_time = 5256000;
                    }
                }

                if (this.forbid_time == 0) {
                    this.forbid_time = 5256000;
                }

                if (this.forbid_time < 0) {
                    Onlines.getInstance().kick(roleid, this.kickoutReason);
                } else {
                    if (this.reason == null) {
                        this.reason = "";
                    }

                    Onlines.getInstance().send(roleid, new SGACDKickoutMsg1(this.reason));
                    Executor.getInstance().schedule(new Runnable() {
                        public void run() {
                            Onlines.getInstance().kick(roleid, -1000);
                        }
                    }, 5L, TimeUnit.SECONDS);
                }

                if (this.forbid_time < 0) {
                    this.forbid_time = 0;
                }

                if (this.auForbid) {
                }

                PunishRecord record = Pod.newPunishRecord();
                record.setGmuserid(this.gmuserid);
                record.setForbidtime((long)(this.forbid_time * 60) * 1000L);
                record.setOptime(System.currentTimeMillis());
                record.setReason(this.reason);
                record.setRoleid(0L);
                record.setType(1);
                record.setUserid(this.dstuserid);
                userpunish.getRecords().add(record);
                if (!this.auForbid) {
                    userpunish.setReleasetime(record.getOptime() + record.getForbidtime());
                }

                try {
                    logger.info("forbid user.gmuserid:" + this.gmuserid + "userid:" + this.dstuserid + "time:" + this.forbid_time + this.reason);
                } catch (Exception e) {
                    LogManager.logger.error(e);
                }

                return true;
            }
        }
    }

    public static int getExtraProb(int userid) {
        return extraProbMap.containsKey(userid) ? (Integer)extraProbMap.get(userid) : 0;
    }
}
