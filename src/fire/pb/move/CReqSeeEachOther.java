//
//

package fire.pb.move;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import fire.pb.main.ConfigManager;
import fire.pb.map.MapConfig;
import fire.pb.scene.Scene;
import fire.pb.scene.manager.RoleManager;
import fire.pb.scene.movable.Role;
import gnet.link.Onlines;
import com.locojoy.base.Octets;
import java.util.Map;

public class CReqSeeEachOther extends __CReqSeeEachOther__ {
    public static final int PROTOCOL_TYPE = 790486;
    public long roleid;

    protected void process() {
        long reqroleId = Onlines.getInstance().findRoleid(this);
        if (reqroleId >= 0L) {
            Role reqrole = RoleManager.getInstance().getRoleByID(reqroleId);
            Role seerole = RoleManager.getInstance().getRoleByID(this.roleid);
            if (reqrole != null && seerole != null) {
                if (reqrole.getScene() == seerole.getScene()) {
                    Scene s = reqrole.getScene();
                    int reqscreenindex = s.getScreenIndex(reqrole.getPos());
                    int seescreenindex = s.getScreenIndex(seerole.getPos());
                    if (Math.abs(reqscreenindex - seescreenindex) <= 1) {
                        MapConfig mapcfg = (MapConfig)ConfigManager.getInstance().getConf(MapConfig.class).get(s.getMapID());
                        if (mapcfg != null) {
                            if (mapcfg.getVisibletype() != 1) {
                                if (mapcfg.getVisibletype() == 2) {
                                    long reqteamid = reqrole.getTeamID();
                                    long addteamid = seerole.getTeamID();
                                    if (reqteamid == 0L || addteamid == 0L || reqteamid != addteamid) {
                                        return;
                                    }
                                }

                                reqrole.seeSomeone(seerole);
                                seerole.seeSomeone(reqrole);
                            }
                        }
                    }
                }
            }
        }
    }

    public int getType() {
        return 790486;
    }

    public CReqSeeEachOther() {
    }

    public CReqSeeEachOther(long _roleid_) {
        this.roleid = _roleid_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.roleid);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.roleid = _os_.unmarshal_long();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof CReqSeeEachOther) {
            CReqSeeEachOther _o_ = (CReqSeeEachOther)_o1_;
            return this.roleid == _o_.roleid;
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += (int)this.roleid;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.roleid).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(CReqSeeEachOther _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = Long.signum(this.roleid - _o_.roleid);
            return 0 != _c_ ? _c_ : _c_;
        }
    }
}
