//
//

package fire.pb.clan.fight;

import fire.pb.activity.clanfight.ActivityClanFightManager;
import fire.pb.clan.PAddClanMoneyByClanid;
import java.util.List;
import mkdb.Procedure;
import xbean.ClanFightRaceRankList;
import xbean.ClanFightRaceRankRecord;
import xtable.Clanfightracelist;

public class PClanFightAddRankMoney extends Procedure {
    protected boolean process() throws Exception {
        long cur = System.currentTimeMillis();
        ClanFightRaceRankList list = Clanfightracelist.select(ActivityClanFightManager.GetMonday0000ByTime(cur));
        if (list != null) {
            List<ClanFightRaceRankRecord> records = list.getRecords();
            if (records != null) {
                int size = records.size();
                if (size > 50) {
                    size = 50;
                }

                for(int i = 0; i < size; ++i) {
                    ClanFightRaceRankRecord r = (ClanFightRaceRankRecord)records.get(i);
                    if (r != null) {
                        int money = 1000;
                        if (i == 0) {
                            money = 6000;
                        } else if (i > 0 && i < 3) {
                            money = 4000;
                        } else if (i >= 3 && i < 10) {
                            money = 2000;
                        }

                        PAddClanMoneyByClanid addmoney = new PAddClanMoneyByClanid(r.getMarshaldata().getClanid(), money);
                        Procedure.pexecuteWhileCommit(addmoney);
                    }
                }
            }
        }

        return true;
    }
}
