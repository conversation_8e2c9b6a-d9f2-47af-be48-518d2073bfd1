//
//

package fire.pb.clan.fight;

import fire.log.enums.YYLoggerTuJingEnum;
import fire.pb.PAddClanPointProc;
import fire.pb.PAddExpProc;
import fire.pb.activity.clanfight.ActivityClanFightManager;
import fire.pb.talk.MessageMgr;
import fire.pb.util.BagUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import mkdb.Lockeys;
import mkdb.Procedure;
import xbean.ClanInfo;
import xbean.EClanFightStatistics;
import xtable.Clans;
import xtable.Locks;
import xtable.Roleid2clanfightstatistics;
import mkdb.Bean;

public class PClanFightBattleHandleAward extends Procedure {
    long clanfightid;
    long attackerid;
    long attackerclanid;
    long hitclanid;
    List<Long> winners;
    List<Long> losers;

    public PClanFightBattleHandleAward(long attackerid, long clanfightid, List<Long> winners, List<Long> losers, long attackerclanid, long hitclanid) {
        this.attackerid = attackerid;
        this.clanfightid = clanfightid;
        this.winners = winners;
        this.losers = losers;
        this.attackerclanid = attackerclanid;
        this.hitclanid = hitclanid;
    }

    protected boolean process() throws Exception {
        long cur = System.currentTimeMillis();
        Set<Long> setclanids = new HashSet();
        setclanids.add(this.attackerclanid);
        setclanids.add(this.hitclanid);
        this.lock(Lockeys.get(Locks.CLANS, setclanids));
        ClanInfo attackerclan = Clans.get(this.attackerclanid);
        ClanInfo hitclan = Clans.get(this.hitclanid);
        ClanFightBattleField bf = ClanFightFactory.getClanFightBattleField(this.clanfightid, false);
        if (bf == null) {
            return true;
        } else if (bf.getClanfightBean().getState() == 2) {
            return true;
        } else {
            Set<Long> roleids = new HashSet();
            roleids.addAll(this.winners);
            roleids.addAll(this.losers);
            this.lock(Lockeys.get(Locks.ROLELOCK, roleids));
            if (this.winners.size() != 0) {
                int totalscroe = this.losers.size() * ActivityClanFightManager.ONE_PEOPLE_SCROE;
                int whichwin = bf.getSideByRoleId((Long)this.winners.get(0));
                boolean attackerside = false;
                if (this.winners.contains(this.attackerid)) {
                    attackerside = true;
                }

                boolean first = true;

                for(Long f : this.winners) {
                    long roleid = f;
                    EClanFightStatistics statistics = Roleid2clanfightstatistics.get(roleid);
                    if (statistics != null) {
                        statistics.setScore(statistics.getScore() + totalscroe / this.winners.size());
                        bf.setRoleIdScore(roleid, statistics.getScore());
                        bf.scroeSort(whichwin, false);
                        statistics.setWinnum(statistics.getWinnum() + 1);
                        if (attackerside) {
                            Integer subact = ActivityClanFightManager.ATTACK_SUB_ACT;
                            int v = statistics.getAct() - subact;
                            if (v < 0) {
                                v = 0;
                            }

                            statistics.setAct(v);
                            List<String> p = new ArrayList();
                            String s = subact.toString();
                            p.add(s);
                            MessageMgr.psendMsgNotifyWhileCommit(roleid, 410039, 0, p);
                        }

                        SBattleFieldAct msg = new SBattleFieldAct();
                        msg.roleact = statistics.getAct();
                        Procedure.psendWhileCommit(roleid, msg);
                        Integer addscroe = totalscroe;
                        bf.addClanScore(whichwin, addscroe);
                        List<String> p = new ArrayList();
                        String s = addscroe.toString();
                        p.add(s);
                        MessageMgr.psendMsgNotifyWhileCommit(roleid, 410033, 0, p);
                        if (statistics.getWinnum() == 5) {
                            BagUtil.addItem(roleid, ActivityClanFightManager.ITEM_ID_LIANSHENG_BOX, 1, "连胜宝箱", YYLoggerTuJingEnum.tujing_Value_liansheng_reward, 1, true);
                            ClanFightBattleField.logger.info("PClanFightBattleHandleAward: 角色:[" + roleid + "] 5连胜 ");
                        }

                        if (statistics.getWinnum() >= 5) {
                            List<String> param = new ArrayList();
                            if (first) {
                                Integer v = statistics.getWinnum();
                                String strnum = v.toString();
                                String name = (String)bf.getClanfightBean().getEnterroleids().get(roleid);
                                if (name != null) {
                                    param.add(name);
                                }

                                param.add(strnum);
                                if (attackerside) {
                                    if (attackerclan != null) {
                                        MessageMgr.psendMsgNotifyWhileCommit(attackerclan.getMembers().keySet(), 410034, 0, param);
                                    }

                                    if (hitclan != null) {
                                        MessageMgr.psendMsgNotifyWhileCommit(hitclan.getMembers().keySet(), 410035, 0, param);
                                    }
                                } else {
                                    if (attackerclan != null) {
                                        MessageMgr.psendMsgNotifyWhileCommit(attackerclan.getMembers().keySet(), 410035, 0, param);
                                    }

                                    if (hitclan != null) {
                                        MessageMgr.psendMsgNotifyWhileCommit(hitclan.getMembers().keySet(), 410034, 0, param);
                                    }
                                }

                                first = false;
                            }
                        }

                        if (statistics.getAct() <= 0) {
                            Double expAward = (double)(400 * Math.min(50, 59)) * 0.0392 * 11.500000000000002;
                            pexecuteWhileCommit(new PAddExpProc(roleid, expAward.longValue(), 23, "公会踢出战场加经验"));
                            Procedure.pexecuteWhileCommit(new PClanFightLeaveClanFieldBattleField(this.clanfightid, roleid, true, true));
                        }

                        Procedure.pexecuteWhileCommit(new PAddClanPointProc(roleid, ActivityClanFightManager.CLAN_FIGHT_WIN_DKP, YYLoggerTuJingEnum.tujing_Value_clanfight_dkp, true, "公会战"));
                    }
                }
            }

            if (this.losers.size() != 0) {
                int whichlose = bf.getSideByRoleId((Long)this.losers.get(0));
                boolean attackerside = false;
                if (this.losers.contains(this.attackerid)) {
                    attackerside = true;
                }

                boolean onepeople = false;
                if (this.losers.size() == 1) {
                    onepeople = true;
                }

                for(Long f : this.losers) {
                    long roleid = f;
                    EClanFightStatistics statistics = Roleid2clanfightstatistics.get(roleid);
                    if (statistics != null) {
                        statistics.setWinnum(0);
                        int oldact = statistics.getAct();
                        int v = statistics.getAct() - ActivityClanFightManager.PK_FAIL_SUB_ACT;
                        if (v < 0) {
                            v = 0;
                        }

                        statistics.setAct(v);
                        if (attackerside) {
                            v = statistics.getAct() - ActivityClanFightManager.ATTACK_SUB_ACT;
                            if (v < 0) {
                                v = 0;
                            }

                            statistics.setAct(v);
                        }

                        Integer subact = oldact - statistics.getAct();
                        List<String> p = new ArrayList();
                        String s = subact.toString();
                        p.add(s);
                        MessageMgr.psendMsgNotifyWhileCommit(roleid, 410039, 0, p);
                        SBattleFieldAct msg = new SBattleFieldAct();
                        msg.roleact = statistics.getAct();
                        Procedure.psendWhileCommit(roleid, msg);
                        if (onepeople) {
                            Integer addscroe = ActivityClanFightManager.ONE_POPLE_FAIL_ADD_SCROE;
                            statistics.setScore(statistics.getScore() + addscroe);
                            bf.setRoleIdScore(roleid, statistics.getScore());
                            bf.addClanScore(whichlose, addscroe);
                            bf.scroeSort(whichlose, false);
                            List<String> param = new ArrayList();
                            String strscroe = addscroe.toString();
                            param.add(strscroe);
                            MessageMgr.psendMsgNotifyWhileCommit(roleid, 410033, 0, param);
                        }

                        if (statistics.getAct() <= 0) {
                            Double expAward = (double)(400 * Math.min(50, 59)) * 0.0392 * 11.500000000000002;
                            pexecuteWhileCommit(new PAddExpProc(roleid, expAward.longValue(), 23, "公会踢出战场加经验"));
                            Procedure.pexecuteWhileCommit(new PClanFightLeaveClanFieldBattleField(this.clanfightid, roleid, true, true));
                        } else {
                            bf.randomGotoPos(roleid);
                        }

                        statistics.setLastlosestamp(cur);
                        Procedure.pexecuteWhileCommit(new PAddClanPointProc(roleid, ActivityClanFightManager.CLAN_FIGHT_LOSE_DKP, YYLoggerTuJingEnum.tujing_Value_clanfight_dkp, true, "公会战"));
                    }
                }
            }

            return true;
        }
    }
}
