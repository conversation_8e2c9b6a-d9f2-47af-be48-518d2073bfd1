//
//

package fire.pb.clan.fight;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Octets;

public class SClanFightOver extends __SClanFightOver__ {
    public static final int PROTOCOL_TYPE = 808543;
    public int status;
    public long overtimestamp;

    protected void process() {
    }

    public int getType() {
        return 808543;
    }

    public SClanFightOver() {
    }

    public SClanFightOver(int _status_, long _overtimestamp_) {
        this.status = _status_;
        this.overtimestamp = _overtimestamp_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.status);
            _os_.marshal(this.overtimestamp);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.status = _os_.unmarshal_int();
        this.overtimestamp = _os_.unmarshal_long();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof SClanFightOver) {
            SClanFightOver _o_ = (SClanFightOver)_o1_;
            if (this.status != _o_.status) {
                return false;
            } else {
                return this.overtimestamp == _o_.overtimestamp;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.status;
        _h_ += (int)this.overtimestamp;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.status).append(",");
        _sb_.append(this.overtimestamp).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(SClanFightOver _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = this.status - _o_.status;
            if (0 != _c_) {
                return _c_;
            } else {
                _c_ = Long.signum(this.overtimestamp - _o_.overtimestamp);
                return 0 != _c_ ? _c_ : _c_;
            }
        }
    }
}
