//
//

package fire.pb.battle.pvp5;

import java.util.Map;
public class PvP5CampPolicy$PolicyGeneralCamp extends PvP5CampPolicy.Policy {
    protected PvP5CampPolicy$PolicyGeneralCamp(long roleId, int grade) {
        super(roleId, grade);
    }

    public void divide() {
        int roleNumCampA = PvP5CampPolicy.getRoleNumByCamp(this.grade, 0);
        int roleNumCampB = PvP5CampPolicy.getRoleNumByCamp(this.grade, 1);
        if (roleNumCampA <= roleNumCampB) {
            this.camp = 0;
        } else {
            this.camp = 1;
        }

        this.mapId = PvP5Helper.getMapIdByRaceGrade(this.grade, false, this.camp);
    }
}
