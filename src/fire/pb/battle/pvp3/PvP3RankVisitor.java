//
//

package fire.pb.battle.pvp3;

import fire.log.enums.YYLoggerTuJingEnum;
import fire.pb.battle.pvp.IPvPVisitor;
import fire.pb.battle.pvp.PvPRaceProxy;
import fire.pb.common.SCommon;
import fire.pb.main.ConfigManager;
import fire.pb.map.SceneManager;
import fire.pb.talk.MessageMgr;
import fire.pb.util.BagUtil;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import mkdb.Procedure;
import mkdb.Transaction;
import mkdb.logs.LogList;
import xbean.Pod;
import xbean.PvP3RaceRole;
import xbean.PvP3ScoreRankList;
import xbean.PvP3ScoreRecord;
import xtable.Properties;
import xtable.Pvp3historyscorelist;

public class PvP3RankVisitor implements IPvPVisitor {
    private final long roleId;
    private final int addScore;

    public PvP3RankVisitor(long var1, int var3) {
        this.roleId = var1;
        this.addScore = var3;
    }

    public void visit(PvPRaceProxy var1) {
        PvP3RaceProxy var2 = (PvP3RaceProxy)var1;
        if (this.roleId != 0L) {
            updateHistoryRankListOfRole(var2.getGrade(), this.roleId, this.addScore);
        } else {
            updateHistoryRankList(var2);
        }

    }

    public static void updateHistoryRankListOfRole(int var0, long var1, int var3) {
        if (var3 > 0) {
            int var4 = getRecordMax();
            PvP3ScoreRankList var5 = Pvp3historyscorelist.get(var0);
            if (var5 == null) {
                var5 = Pod.newPvP3ScoreRankList();
                Pvp3historyscorelist.add(var0, var5);
            }

            int var6 = var3;
            PvP3ScoreRecord var7 = null;
            Iterator var8 = var5.getRecords().iterator();

            while(var8.hasNext()) {
                PvP3ScoreRecord var9 = (PvP3ScoreRecord)var8.next();
                if (var9.getRoleid() == var1) {
                    var8.remove();
                    var7 = var9;
                    var6 = var3 + var9.getScore();
                    break;
                }
            }

            if (var7 == null) {
                String var11 = Properties.selectRolename(var1);
                var7 = Pod.newPvP3ScoreRecord();
                var7.setRoleid(var1);
                var7.setRolename(var11);
            }

            var7.setScore(var6);
            boolean var12 = false;
            ListIterator var13 = ((LogList)var5.getRecords()).listIterator();

            while(var13.hasNext()) {
                PvP3ScoreRecord var10 = (PvP3ScoreRecord)var13.next();
                if (var6 > var10.getScore()) {
                    var13.previous();
                    var13.add(var7);
                    var12 = true;
                    break;
                }
            }

            if (!var12) {
                var5.getRecords().add(var7);
            }

            resize(var5.getRecords(), var4);
        }
    }

    public static void updateHistoryRankList(PvP3RaceProxy var0) {
        int var1 = getRecordMax();
        List var2 = getCurrentRankListAndSort(var0, var1);
        PvP3ScoreRankList var3 = Pvp3historyscorelist.get(var0.getGrade());
        if (var3 == null) {
            var3 = Pod.newPvP3ScoreRankList();
            Pvp3historyscorelist.add(var0.getGrade(), var3);
        }

        for(PvP3ScoreRecord var5 : var3.getRecords()) {
            Iterator var6 = var2.iterator();

            while(var6.hasNext()) {
                PvP3RaceRole var7 = (PvP3RaceRole)var6.next();
                if (var7.getRoleid() == var5.getRoleid()) {
                    var5.setScore(var5.getScore() + var7.getScore());
                    var6.remove();
                    break;
                }
            }
        }

        List var8 = var3.getRecords();

        for(int var9 = 0; var9 < var2.size(); ++var9) {
            PvP3RaceRole var10 = (PvP3RaceRole)var2.get(var9);
            PvP3ScoreRecord var11 = Pod.newPvP3ScoreRecord();
            var11.setRoleid(var10.getRoleid());
            var11.setRolename(var10.getName());
            var11.setScore(var10.getScore());
            var8.add(var11);
        }

        var2.clear();
        sort(var3.getRecords(), getCurrentRankMap(var0));
        resize(var3.getRecords(), var1);
    }

    public static Map<Long, Integer> getCurrentRankMap(PvP3RaceProxy var0) {
        HashMap var1 = new HashMap();
        int var2 = 0;

        for(PvP3RaceRole var4 : var0.getXRace().getAllroles()) {
            Long var10001 = var4.getRoleid();
            ++var2;
            var1.put(var10001, var2);
        }

        return var1;
    }

    public static List<PvP3RaceRole> getCurrentRankListAndSort(PvP3RaceProxy var0, int var1) {
        LinkedList var2 = new LinkedList();
        List var3 = var0.getXRace().getAllroles();
        int var4 = Math.min(var1, var3.size());

        for(int var5 = 0; var5 < var4; ++var5) {
            var2.add(var3.get(var5));
        }

        return var2;
    }

    public static void sort(List<PvP3ScoreRecord> var0, Map<Long, Integer> var1) {
        LinkedList var2 = new LinkedList();

        for(PvP3ScoreRecord var4 : var0) {
            var2.add(var4);
        }

        var0.clear();
        var2.sort(new IPvPVisitor.Util.ComparatorRank<PvP3ScoreRecord>(var1) {
            public int compare(PvP3ScoreRecord var1, PvP3ScoreRecord var2) {
                int var3 = var2.getScore() - var1.getScore();
                if (var3 == 0) {
                    var3 = this.compareRank(var1.getRoleid(), var2.getRoleid());
                }

                if (var3 == 0) {
                    var3 = var1.getRoleid() - var2.getRoleid() > 0L ? 1 : -1;
                }

                return var3;
            }
        });
        var0.addAll(var2);
    }

    public static void resize(List<PvP3ScoreRecord> var0, int var1) {
        while(var0.size() > var1) {
            var0.remove(var0.size() - 1);
        }

    }

    public static void faZhouJiang() {
        for(int var4 : PvP3Helper.getAllRaceIds()) {
            if (var4 == 3) {
                PvP3ScoreRankList var5 = Pvp3historyscorelist.select(var4);
                if (null != var5) {
                    List var6 = var5.getRecords();
                    if (!var6.isEmpty()) {
                        var6.sort(new Comparator<PvP3ScoreRecord>() {
                            public int compare(PvP3ScoreRecord var1, PvP3ScoreRecord var2) {
                                int var3 = var2.getScore() - var1.getScore();
                                if (var3 == 0) {
                                    var3 = var1.getRoleid() - var2.getRoleid() > 0L ? 1 : -1;
                                }

                                return var3;
                            }
                        });

                        for(int var7 = 0; var7 < Math.min(var6.size(), 5); ++var7) {
                            SCommon var2 = (SCommon)ConfigManager.getInstance().getConf(SCommon.class).get(476 + var7);
                            if (null != var2) {
                                PvP3ScoreRecord var8 = (PvP3ScoreRecord)var6.get(var7);
                                System.out.println("准备执行周排行>>>>>>>>pvP3ScoreRecord:" + var8.getRolename());
                                Procedure var0 = createAwardProc(var8.getRoleid(), Integer.parseInt(var2.getValue()));
                                if (Transaction.current() != null) {
                                    Procedure.pexecuteWhileCommit(var0);
                                } else {
                                    var0.submit();
                                }

                                ArrayList var9 = new ArrayList(4);
                                var9.add(var8.getRolename());
                                var9.add("" + (var7 + 1));
                                SceneManager.sendAll(MessageMgr.getMsgNotify(191129, 0, var9));
                            }
                        }
                    }
                }
            }
        }

    }

    public static void clearHistoryRankList() {
        for(final int var2 : PvP3Helper.getAllRaceIds()) {
            Procedure var0 = new Procedure() {
                protected boolean process() {
                    Pvp3historyscorelist.remove(var2);
                    return true;
                }
            };
            if (Transaction.current() != null) {
                Procedure.pexecuteWhileCommit(var0);
            } else {
                var0.submit();
            }
        }

    }

    private static Procedure createAwardProc(final long var0, final int var2) {
        return new Procedure() {
            protected boolean process() {
                byte var1 = 1;
                int var2x = BagUtil.addItem(var0, var2, var1, "PVP3排名奖励", YYLoggerTuJingEnum.tujing_Value_PVP3, var2);
                if (var2x < var1) {
                }

                return true;
            }
        };
    }

    public static int getRecordMax() {
        byte var0 = 50;
        int var1 = var0 * 120;
        var1 = Math.max(var1, 0);
        return var1;
    }
}
