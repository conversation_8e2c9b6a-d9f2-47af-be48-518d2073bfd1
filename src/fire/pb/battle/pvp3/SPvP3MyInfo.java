//
//

package fire.pb.battle.pvp3;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Octets;

public class SPvP3MyInfo extends __SPvP3MyInfo__ {
    public static final int PROTOCOL_TYPE = 793634;
    public byte firstwin;
    public byte tenfight;
    public byte eightwin;
    public byte battlenum;
    public byte winnum;
    public short combowinnum;
    public int score;
    public byte ready;

    protected void process() {
    }

    public int getType() {
        return 793634;
    }

    public SPvP3MyInfo() {
    }

    public SPvP3MyInfo(byte _firstwin_, byte _tenfight_, byte _eightwin_, byte _battlenum_, byte _winnum_, short _combowinnum_, int _score_, byte _ready_) {
        this.firstwin = _firstwin_;
        this.tenfight = _tenfight_;
        this.eightwin = _eightwin_;
        this.battlenum = _battlenum_;
        this.winnum = _winnum_;
        this.combowinnum = _combowinnum_;
        this.score = _score_;
        this.ready = _ready_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.firstwin);
            _os_.marshal(this.tenfight);
            _os_.marshal(this.eightwin);
            _os_.marshal(this.battlenum);
            _os_.marshal(this.winnum);
            _os_.marshal(this.combowinnum);
            _os_.marshal(this.score);
            _os_.marshal(this.ready);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.firstwin = _os_.unmarshal_byte();
        this.tenfight = _os_.unmarshal_byte();
        this.eightwin = _os_.unmarshal_byte();
        this.battlenum = _os_.unmarshal_byte();
        this.winnum = _os_.unmarshal_byte();
        this.combowinnum = _os_.unmarshal_short();
        this.score = _os_.unmarshal_int();
        this.ready = _os_.unmarshal_byte();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof SPvP3MyInfo) {
            SPvP3MyInfo _o_ = (SPvP3MyInfo)_o1_;
            if (this.firstwin != _o_.firstwin) {
                return false;
            } else if (this.tenfight != _o_.tenfight) {
                return false;
            } else if (this.eightwin != _o_.eightwin) {
                return false;
            } else if (this.battlenum != _o_.battlenum) {
                return false;
            } else if (this.winnum != _o_.winnum) {
                return false;
            } else if (this.combowinnum != _o_.combowinnum) {
                return false;
            } else if (this.score != _o_.score) {
                return false;
            } else {
                return this.ready == _o_.ready;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.firstwin;
        _h_ += this.tenfight;
        _h_ += this.eightwin;
        _h_ += this.battlenum;
        _h_ += this.winnum;
        _h_ += this.combowinnum;
        _h_ += this.score;
        _h_ += this.ready;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.firstwin).append(",");
        _sb_.append(this.tenfight).append(",");
        _sb_.append(this.eightwin).append(",");
        _sb_.append(this.battlenum).append(",");
        _sb_.append(this.winnum).append(",");
        _sb_.append(this.combowinnum).append(",");
        _sb_.append(this.score).append(",");
        _sb_.append(this.ready).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(SPvP3MyInfo _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = this.firstwin - _o_.firstwin;
            if (0 != _c_) {
                return _c_;
            } else {
                _c_ = this.tenfight - _o_.tenfight;
                if (0 != _c_) {
                    return _c_;
                } else {
                    _c_ = this.eightwin - _o_.eightwin;
                    if (0 != _c_) {
                        return _c_;
                    } else {
                        _c_ = this.battlenum - _o_.battlenum;
                        if (0 != _c_) {
                            return _c_;
                        } else {
                            _c_ = this.winnum - _o_.winnum;
                            if (0 != _c_) {
                                return _c_;
                            } else {
                                _c_ = this.combowinnum - _o_.combowinnum;
                                if (0 != _c_) {
                                    return _c_;
                                } else {
                                    _c_ = this.score - _o_.score;
                                    if (0 != _c_) {
                                        return _c_;
                                    } else {
                                        _c_ = this.ready - _o_.ready;
                                        return 0 != _c_ ? _c_ : _c_;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
