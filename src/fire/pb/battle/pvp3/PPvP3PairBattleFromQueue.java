//
//

package fire.pb.battle.pvp3;

import fire.pb.PropRole;
import fire.pb.circletask.PVPMatchTimeConfig;
import fire.pb.main.ConfigManager;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import mkdb.Mkdb;
import mkdb.Procedure;
import xbean.PvP3QueueRole;
import xtable.Properties;
import xtable.Roleid2teamid;
import java.util.Set;

public class PPvP3PairBattleFromQueue extends Procedure {
    private final int grade;
    public Map<Integer, PVPMatchTimeConfig> matchConfs = ConfigManager.getInstance().getConf(PVPMatchTimeConfig.class);

    public PPvP3PairBattleFromQueue(int var1) {
        this.grade = var1;
    }

    protected boolean process() {
        PvP3RaceProxy var1 = PvP3RaceProxy.getPvP3RaceProxy(this.grade);
        if (var1.getXRace() == null) {
            return false;
        } else if (!var1.isOpening()) {
            return false;
        } else if (var1.getXRace().getWaitingqueue().size() < 2) {
            return false;
        } else {
            long var2 = System.currentTimeMillis();
            ArrayList var4 = new ArrayList();
            Iterator var5 = var1.getXRace().getWaitingqueue().iterator();

            while(var5.hasNext()) {
                PvP3QueueRole var6 = (PvP3QueueRole)var5.next();
                PvP3RoleProxy var7 = getValidRole(var1, var6.getRoleid(), var2);
                if (var7 == null) {
                    var5.remove();
                    PvP3RoleProxy.setTeamMemberPvPState(var6.getRoleid(), 0);
                    PvP3RoleProxy.notifyTeamProtocol(var6.getRoleid(), new SPvP3ReadyFight((byte)0));
                } else {
                    var4.add(var7);
                }
            }

            int var8 = var4.size();
            int var9 = this.selectPair(var1, var4, 0, 0, var2);
            if (PvP3Control.getLogger().isInfoEnabled()) {
                PvP3Control.getLogger().info("PVP3::[PPvP3PairBattleFromQueue] pair end. grade:" + this.grade + " queueSize:" + var8 + " pairNum:" + var9 + " roleNum:" + var1.getXRace().getAllrolesid().size() + " rankNum:" + var1.getXRace().getAllroles().size());
            }

            return true;
        }
    }

    private static PvP3RoleProxy getValidRole(PvP3RaceProxy var0, long var1, long var3) {
        PvP3RoleProxy var5 = null;

        for(long var8 : PvP3RoleProxy.getTeamMemberIds(var1)) {
            PvP3RoleProxy var10 = PvP3RoleProxy.getPvP3RoleProxy(var8, true);
            if (!var0.roleIsCanReady(var10.getRoleId(), false)) {
                return null;
            }

            if (var10.getRoleId() == var1) {
                var5 = var10;
            }
        }

        return var5;
    }

    private int selectPair(PvP3RaceProxy var1, List<PvP3RoleProxy> var2, int var3, int var4, long var5) {
        int var7 = var1.getXRace().getWaitingqueue().size();
        if (var3 >= var7 - 1) {
            return var4;
        } else {
            PvP3QueueRole var8 = (PvP3QueueRole)var1.getXRace().getWaitingqueue().get(var3);
            PvP3RoleProxy var9 = (PvP3RoleProxy)var2.get(var3);
            if (!roleCanFight(var9, var5)) {
                return this.selectPair(var1, var2, var3 + 1, var4, var5);
            } else {
                long var10 = Math.max(0L, var5 - var8.getEnterqueuetime());

                for(int var12 = var3 + 1; var12 < var7; ++var12) {
                    PvP3RoleProxy var13 = (PvP3RoleProxy)var2.get(var12);
                    if (roleCanFight(var13, var5) && this.pair(var9, var13, var10)) {
                        var1.getXRace().getWaitingqueue().remove(var3);
                        var2.remove(var3);
                        var1.getXRace().getWaitingqueue().remove(var12 - 1);
                        var2.remove(var12 - 1);
                        sendMatchResult(var9.getRoleId(), var13.getRoleId());
                        sendMatchResult(var13.getRoleId(), var9.getRoleId());
                        NewBattle(this.grade, var9.getRoleId(), var13.getRoleId(), var5);
                        return this.selectPair(var1, var2, var3, var4 + 1, var5);
                    }
                }

                return this.selectPair(var1, var2, var3 + 1, var4, var5);
            }
        }
    }

    private boolean pair(PvP3RoleProxy var1, PvP3RoleProxy var2, long var3) {
        if (var1.getRoleId() == var2.getRoleId()) {
            return false;
        } else {
            int var5 = var1.getScoreLevel();
            int var6 = var2.getScoreLevel();
            int var7 = (int)(var3 / 1000L) + 1;
            int var8 = 999;
            PVPMatchTimeConfig var9 = (PVPMatchTimeConfig)this.matchConfs.get(var7);
            if (var9 != null) {
                var8 = var9.lev;
            }

            int var10 = Properties.selectLevel(var1.getRoleId());
            int var11 = Properties.selectLevel(var2.getRoleId());
            return Math.abs(var5 - var6) + Math.abs(var10 - var11) / 5 <= var8;
        }
    }

    public static boolean roleCanFight(PvP3RoleProxy var0, long var1) {
        return var1 - var0.getLastBattleTime() >= 5000L;
    }

    public static void sendMatchResult(long var0, long var2) {
        List var4 = PvP3RoleProxy.getTeamMemberIds(var2);
        SPvP3MatchResult var5 = new SPvP3MatchResult();

        for(long var7 : var4) {
            PropRole var9 = new PropRole(var7, true);
            PvP3RoleSingleMatch var10 = new PvP3RoleSingleMatch();
            var10.roleid = var7;
            var10.level = (short)var9.getLevel();
            var10.shape = var9.getShape();
            var10.school = var9.getSchool();
            var5.targets.add(var10);
        }

        PvP3RoleProxy.notifyTeamProtocol(var0, var5);
    }

    private static void NewBattle(final int var0, final long var1, final long var3, final long var5) {
        final List var7 = PvP3RoleProxy.getTeamMemberIds(var1);
        final List var8 = PvP3RoleProxy.getTeamMemberIds(var3);
        PvP3RoleProxy.setTeamMemberPvPState(var1, 2);
        PvP3RoleProxy.setTeamMemberPvPState(var3, 2);
        Procedure.pexecuteWhileCommit(new Procedure() {
            public boolean process() {
                Mkdb.executor().schedule(new Runnable() {
                    public void run() {
                        (new Procedure() {
                            public boolean process() {
                                boolean var1x = true;
                                PvP3RaceProxy var2 = PvP3RaceProxy.getPvP3RaceProxy(var0);
                                if (var2.getXRace() == null) {
                                    var1x = false;
                                }

                                HashSet var3x = new HashSet();
                                if (var1x) {
                                    List var4 = PvP3RoleProxy.getTeamMemberIds(var1);
                                    if (var4.size() > 5 || var4.size() != var7.size() || !var4.containsAll(var7)) {
                                        System.out.println(">>>>>>>>>>主队人数问题");
                                        var1x = false;
                                    }

                                    var3x.addAll(var4);
                                }

                                if (var1x) {
                                    List var10 = PvP3RoleProxy.getTeamMemberIds(var3);
                                    if (var10.size() > 5 || var10.size() != var8.size() || !var10.containsAll(var8)) {
                                        System.out.println(">>>>>>>>>>客队人数问题");
                                        var1x = false;
                                    }

                                    for(long var6 : var10) {
                                        if (var3x.contains(var6)) {
                                            System.out.println(">>>>>>>>>>队员重复问题");
                                            var1x = false;
                                            break;
                                        }
                                    }
                                }

                                if (var1x && PPvP3PairBattleFromQueue.getValidRole(var2, var1, var5) == null) {
                                    System.out.println(">>>>>>>>>>有效性问题1");
                                    var1x = false;
                                }

                                if (var1x && PPvP3PairBattleFromQueue.getValidRole(var2, var1, var5) == null) {
                                    System.out.println(">>>>>>>>>>有效性问题2");
                                    var1x = false;
                                }

                                if (var1x) {
                                    long var11 = -1L;
                                    long var12 = -1L;
                                    Long var8x = Roleid2teamid.select(var1);
                                    if (var8x != null) {
                                        var11 = var8x;
                                    }

                                    Long var9 = Roleid2teamid.select(var3);
                                    if (var9 != null) {
                                        var12 = var9;
                                    }

                                    Procedure.pexecuteWhileCommit(new PPvP3NewBattle(var0, var1, var3, var11, var12));
                                } else {
                                    PvP3RoleProxy.notifyTeamProtocol(var1, new SPvP3ReadyFight((byte)0));
                                    PvP3RoleProxy.notifyTeamProtocol(var3, new SPvP3ReadyFight((byte)0));
                                    PvP3RoleProxy.setTeamMemberPvPState(var1, 0);
                                    PvP3RoleProxy.setTeamMemberPvPState(var3, 0);
                                }

                                return true;
                            }
                        }).submit();
                    }
                }, 3L, TimeUnit.SECONDS);
                return true;
            }
        });
    }
}
