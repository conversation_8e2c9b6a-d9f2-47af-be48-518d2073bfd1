//
//

package fire.pb.battle.ai;

import fire.pb.util.Parser;
import java.util.LinkedList;
import java.util.List;

public class MonsterBattleAI {
    private final int monsterId;
    private List<Integer> battleAIs = new LinkedList();
    private List<Parser.ID2Odds> battleAIOdds = new LinkedList();

    public MonsterBattleAI(int monsterId) {
        this.monsterId = monsterId;
    }

    public int getMonsterId() {
        return this.monsterId;
    }

    public List<Integer> getBattleAIs() {
        return this.battleAIs;
    }

    public List<Parser.ID2Odds> getBattleAIOdds() {
        return this.battleAIOdds;
    }
}
