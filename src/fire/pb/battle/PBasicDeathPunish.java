//
//

package fire.pb.battle;

import fire.pb.attr.SRefreshRoleData;
import fire.pb.effect.Role;
import fire.pb.effect.RoleImpl;
import fire.pb.main.ConfigManager;
import java.util.Map;
import mkdb.Procedure;

public class PBasicDeathPunish extends Procedure implements Runnable {
    private final long roleid;
    private final int battleid;

    public PBasicDeathPunish(long roleid, int battleid) {
        this.roleid = roleid;
        this.battleid = battleid;
    }

    protected boolean process() throws Exception {
        Role role = new RoleImpl(this.roleid);
        Map<Integer, Float> result = role.fullHpAndRecoverWound();
        role.setHp(1);
        SRefreshRoleData sr = new SRefreshRoleData();
        sr.datas.putAll(result);
        sr.datas.put(80, (float)role.getHp());
        sr.datas.put(100, (float)role.getMp());
        Procedure.psendWhileCommit(this.roleid, sr);
        psendWhileCommit(this.roleid, new SDeadLess20(1));
        SBattleInfo battleinfo = (SBattleInfo)ConfigManager.getInstance().getConf(SBattleInfo.class).get(this.battleid);
        if (battleinfo != null && battleinfo.getDeathinfo() == 1) {
            psendWhileCommit(this.roleid, new SDeadLess20(0));
        }

        if (battleinfo == null) {
            psendWhileCommit(this.roleid, new SDeadLess20(0));
        }

        return true;
    }

    public void run() {
        this.submit();
    }
}
