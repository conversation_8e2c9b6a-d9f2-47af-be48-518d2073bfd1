//
//

package fire.pb.gm;

import fire.pb.activity.award.RewardMgr;
import java.util.concurrent.TimeUnit;
import mkdb.Executor;

public class GM_starttripple extends GMCommand {
    boolean exec(String[] args) {
        RewardMgr.setIwebMultiExp(2);
        this.sendToGM("全服三倍已经开启!");
        Executor.getInstance().schedule(new Runnable() {
            public void run() {
                GMCommand command = new GM_stoptripple();
                command.setGmLocalsid(GM_starttripple.this.getGmLocalsid());
                command.setGmroleid(GM_starttripple.this.getGmroleid());
                command.setGmUserid(GM_starttripple.this.getGmUserid());
                command.exec((String[])null);
            }
        }, 2L, TimeUnit.HOURS);
        return true;
    }

    String usage() {
        return null;
    }
}
