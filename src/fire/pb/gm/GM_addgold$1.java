//
//

package fire.pb.gm;

import fire.log.enums.YYLoggerTuJingEnum;
import fire.pb.item.Pack;
import mkdb.Procedure;

class GM_addgold$1 extends Procedure {
    GM_addgold$1(GM_addgold this$0, long var2, long var4) {
        this.this$0 = this$0;
        this.val$roleid = var2;
        this.val$money = var4;
    }

    protected boolean process() {
        Pack bag = new Pack(this.val$roleid, false);
        bag.addSysGold(this.val$money, "GM指令 加金币", YYLoggerTuJingEnum.GM, 0);
        return true;
    }
}
