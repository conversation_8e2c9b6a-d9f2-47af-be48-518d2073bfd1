//
//

package fire.pb.gm;

import fire.log.enums.YYLoggerTuJingEnum;
import fire.pb.item.AddItemResult;
import fire.pb.item.EquipItem;
import fire.pb.item.Module;
import fire.pb.item.Pack;
import mkdb.Procedure;

class GM_addequipitem$1 extends Procedure {
    GM_addequipitem$1(GM_addequipitem this$0, int var2) {
        this.this$0 = this$0;
        this.val$id = var2;
    }

    protected boolean process() {
        EquipItem equipitem = (EquipItem)Module.getInstance().getItemManager().genItemBase(this.val$id, 1);
        if (equipitem == null) {
            return false;
        } else {
            Pack bag = new Pack(this.this$0.getGmroleid(), false);
            return bag.doAddItem(equipitem, -1, "GM add", YYLoggerTuJingEnum.GM, 0) == AddItemResult.SUCC;
        }
    }
}
