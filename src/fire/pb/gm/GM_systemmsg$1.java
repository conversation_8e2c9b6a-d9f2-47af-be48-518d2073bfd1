//
//

package fire.pb.gm;

import com.locojoy.base.Octets;
import fire.pb.friends.SSendSystemMessageToRole;
import fire.pb.map.SceneManager;
import fire.pb.talk.MessageMgr;
import java.util.ArrayList;
import java.util.List;
import mkdb.Procedure;

class GM_systemmsg$1 extends Procedure {
    GM_systemmsg$1(GM_systemmsg this$0, int var2, int var3, List var4) {
        this.this$0 = this$0;
        this.val$type = var2;
        this.val$msgId = var3;
        this.val$param = var4;
    }

    protected boolean process() throws Exception {
        if (this.val$type == 0) {
            MessageMgr.psendSystemMessageToRole(this.this$0.getGmroleid(), this.val$msgId, this.val$param);
        } else {
            ArrayList<Octets> octetsList = new ArrayList();

            for(String s : this.val$param) {
                octetsList.add(GM_systemmsg.convertString2Octets(s));
            }

            SSendSystemMessageToRole msg = new SSendSystemMessageToRole();
            msg.systemroleid = 0L;
            msg.contentid = this.val$msgId;
            msg.contentparam = octetsList;
            SceneManager.sendAll(msg);
        }

        return true;
    }
}
