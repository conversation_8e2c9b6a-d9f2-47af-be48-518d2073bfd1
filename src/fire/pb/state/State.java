//
//

package fire.pb.state;

public abstract class State implements IState {
    public static final int UNENTRY_STATE = 0;
    public static final int PRE_ENTRY_STATE = 1;
    public static final int ENTRY_STATE = 2;
    public static final int PRE_OFFLINE_PROTECT_STATE = 3;
    public static final int OFFLINE_PROTECT_STATE = 4;
    public static final int BREAK_OFFLINE_PROTECT_STATE = 5;
    public static final int END_OFFLINE_PROTECT_STATE = 6;
    public static final int PRE_TRUSTEESHIP_STATE = 7;
    public static final int TRUSTEESHIP_STATE = 8;
    public static final int BREAK_TRUSTEESHIP_STATE = 9;
    public static final int END_TRUSTEESHIP_STATE = 10;
    public static final int TRIGGER_ONLINE = 0;
    public static final int TRIGGER_OFFLINE = 1;
    public static final int TRIGGER_OFFLINE_BATTLE = 2;
    public static final int TRIGGER_PROCESS_DONE = 3;
    public static final int TRIGGER_TIME_OUT = 4;
    public static final int TRIGGER_BATTLE_END = 5;
    public static final int TRIGGER_OFFLINE_CHOSEE_ROLE = 6;
    public static final int TRIGGER_OFFLINE_LINK_BROKEN = 7;
    public static final int TRIGGER_CLIENT_LOCK_SCREEN = 8;
    protected final long roleId;

    public State(long roleId) {
        this.roleId = roleId;
    }

    protected abstract boolean execute();

    protected void triggerErrorLog(int trigger) {
        StateManager.logger.error((new StringBuilder("roleId=")).append(this.roleId).append(" 角色状态").append(this.getClass().getCanonicalName()).append("转移失败，trigger = ").append(trigger));
    }

    protected void enterErrorLog(int oldstate, int trigger) {
        StateManager.logger.error((new StringBuilder("roleId=")).append(this.roleId).append(" 角色状态").append(this.getClass().getCanonicalName()).append("转移失败，oldstate = ").append(oldstate).append("，trigger = ").append(trigger));
    }
}
