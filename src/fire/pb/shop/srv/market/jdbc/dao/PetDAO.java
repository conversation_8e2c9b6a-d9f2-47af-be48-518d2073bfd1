//
//

package fire.pb.shop.srv.market.jdbc.dao;

import fire.pb.shop.MarketGoods;
import fire.pb.shop.MarketSearchAttr;
import fire.pb.shop.srv.market.MarketManager;
import fire.pb.shop.srv.market.jdbc.JdbcTemplate;
import fire.pb.shop.srv.market.jdbc.MarketDAO;
import fire.pb.shop.srv.market.jdbc.Page;
import fire.pb.shop.srv.market.jdbc.SQL;
import fire.pb.shop.srv.market.jdbc.utils.handler.ArrayHandler;
import fire.pb.shop.srv.market.pojo.MarketGoodsHandler;
import fire.pb.shop.srv.market.pojo.PetDAOHandler;
import fire.pb.shop.srv.market.pojo.PetDaoBean;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import mkdb.Bean;

public class PetDAO extends MarketDAO {
    private PetDAO() {
    }

    public static PetDAO getInstance() {
        return PetDAO.PetDAOHolder.instance;
    }

    public boolean addPet(PetDaoBean petDaoBean) {
        StringBuilder sql = new StringBuilder("INSERT INTO ITEM_PET(ID, FIRSTNO, TWONO, THREENO, UNIQUEID, KEY, ROLEID, ITEMID, NAME, LEVEL, ATTACK, DEFEND, SPEED, MAGICATTACK, MAGCIDEF, MAXHP, ATTACKAPT, DEFENDAPT, PHYFORCEAPT, MAGICAPT, SPEEDAPT, DODGEAPT, GROWRATE, SKILLNUMBER, PETSCORE, NUMBER, PRICE, ZDID, ATTENTION, SHOWTIME, EXPIRETIME) VALUES(");
        sql.append(petDaoBean.getId()).append(",");
        sql.append(petDaoBean.getFirstno()).append(",");
        sql.append(petDaoBean.getTwono()).append(",");
        sql.append(petDaoBean.getThreeno()).append(",");
        sql.append(petDaoBean.getUniquid()).append(",");
        sql.append(petDaoBean.getKey()).append(",");
        sql.append(petDaoBean.getRoleid()).append(",");
        sql.append(petDaoBean.getItemid()).append(",");
        sql.append("'");
        sql.append(petDaoBean.getName());
        sql.append("'");
        sql.append(",");
        sql.append(petDaoBean.getLevel()).append(",");
        sql.append(petDaoBean.getAttack()).append(",");
        sql.append(petDaoBean.getDefend()).append(",");
        sql.append(petDaoBean.getSpeed()).append(",");
        sql.append(petDaoBean.getMagicattack()).append(",");
        sql.append(petDaoBean.getMagicdef()).append(",");
        sql.append(petDaoBean.getMaxhp()).append(",");
        sql.append(petDaoBean.getAttackapt()).append(",");
        sql.append(petDaoBean.getDefendapt()).append(",");
        sql.append(petDaoBean.getPhyforceapt()).append(",");
        sql.append(petDaoBean.getMagicapt()).append(",");
        sql.append(petDaoBean.getSpeedapt()).append(",");
        sql.append(petDaoBean.getDodgeapt()).append(",");
        sql.append(petDaoBean.getGrowrate()).append(",");
        sql.append(petDaoBean.getSkillNumber()).append(",");
        sql.append(petDaoBean.getPetscore()).append(",");
        sql.append(petDaoBean.getNumber()).append(",");
        sql.append(petDaoBean.getPrice()).append(",");
        sql.append(petDaoBean.getZdid()).append(",");
        sql.append(petDaoBean.getAttentionnumber()).append(",");
        sql.append(petDaoBean.getShowtime()).append(",");
        sql.append(petDaoBean.getExpiretime());
        sql.append(");");
        if (MarketManager.LOG.isDebugEnabled()) {
            MarketManager.LOG.debug("拍卖上架装备物品SQL语句[" + sql.toString() + "]");
        }

        int ret = this.update(sql.toString());
        return ret > 0;
    }

    public PetDaoBean queryPet(long id) {
        StringBuilder sql = new StringBuilder("SELECT * FROM ITEM_PET WHERE ");
        sql.append("id=").append(id).append(" for update");
        if (MarketManager.LOG.isDebugEnabled()) {
            MarketManager.LOG.debug("浏览拍卖宠物物品SQL语句[" + sql.toString() + "]");
        }

        List<PetDaoBean> results = null;

        try {
            results = (List)JdbcTemplate.getInstance().query(sql.toString(), new PetDAOHandler());
        } catch (SQLException e) {
            MarketManager.LOG.error("market查询数据库表记录错误:", e);
        }

        if (results != null && results.size() != 0) {
            if (results.size() <= 1) {
                return results.size() > 0 ? (PetDaoBean)results.get(0) : null;
            } else {
                for(PetDaoBean bean : results) {
                    MarketManager.LOG.error("出现多条数据错误" + bean.toString());
                }

                return null;
            }
        } else {
            return null;
        }
    }

    public List<MarketGoods> queryPet(int browseType, int firstno, int twono, ArrayList<Integer> threeno, long currtime, Page page, int priceSort) {
        StringBuilder sql = new StringBuilder("SELECT * FROM ITEM_PET WHERE ");
        sql.append("firstno=").append(firstno).append(" and ");
        sql.append("twono=").append(twono).append(" and ");
        sql.append("threeno ").append(SQL.inWrapper(threeno)).append(" and ");
        boolean exc = false;
        if (browseType == 1) {
            sql.append("showtime<=").append(currtime).append(" and ");
            sql.append("expiretime>").append(currtime);
            exc = true;
        } else if (browseType == 2) {
            sql.append("showtime>").append(currtime);
            exc = true;
        }

        if (priceSort == 2) {
            sql.append(" order by price desc");
        } else {
            sql.append(" order by price asc");
        }

        if (!exc) {
            return null;
        } else {
            if (MarketManager.LOG.isDebugEnabled()) {
                MarketManager.LOG.debug("浏览拍卖宠物物品SQL语句[" + sql.toString() + "]");
            }

            return this.queryPage(page, sql.toString());
        }
    }

    public int queryPetTotalRow(int browseType, int firstno, int twono, ArrayList<Integer> threeno, long currtime) {
        StringBuilder sql = new StringBuilder("SELECT count(id) FROM ITEM_PET WHERE ");
        sql.append("firstno=").append(firstno).append(" and ");
        sql.append("twono=").append(twono).append(" and ");
        sql.append("threeno ").append(SQL.inWrapper(threeno)).append(" and ");
        boolean exc = false;
        if (browseType == 1) {
            sql.append("showtime<=").append(currtime).append(" and ");
            sql.append("expiretime>").append(currtime);
            exc = true;
        } else if (browseType == 2) {
            sql.append("showtime>").append(currtime);
            exc = true;
        }

        if (!exc) {
            return -1;
        } else {
            if (MarketManager.LOG.isDebugEnabled()) {
                MarketManager.LOG.debug("查询浏览拍卖记录数,宠物物品SQL语句[" + sql.toString() + "]");
            }

            return this.queryTotalRow(sql.toString());
        }
    }

    public MarketGoods queryPet(long roleId, long uniqueId, int itemId, int key) {
        StringBuilder sql = new StringBuilder("SELECT * FROM ITEM_PET WHERE ");
        sql.append("roleId=").append(roleId).append(" and ");
        sql.append("uniqueId=").append(uniqueId).append(" and ");
        sql.append("itemId=").append(itemId).append(" and ");
        sql.append("key=").append(key);
        if (MarketManager.LOG.isDebugEnabled()) {
            MarketManager.LOG.debug("浏览拍卖宠物物品SQL语句[" + sql.toString() + "]");
        }

        List<MarketGoods> results = null;

        try {
            results = (List)JdbcTemplate.getInstance().query(sql.toString(), new MarketGoodsHandler());
        } catch (SQLException e) {
            e.printStackTrace();
        }

        if (results != null && results.size() != 0) {
            if (results.size() <= 1) {
                return results.size() > 0 ? (MarketGoods)results.get(0) : null;
            } else {
                for(MarketGoods goods : results) {
                    MarketManager.LOG.error("出现多条数据错误" + goods.toString());
                }

                return null;
            }
        } else {
            return null;
        }
    }

    public boolean removePet(long id) {
        StringBuilder sql = new StringBuilder("DELETE FROM ITEM_PET WHERE ");
        sql.append("id=").append(id);
        if (MarketManager.LOG.isDebugEnabled()) {
            MarketManager.LOG.debug("删除拍卖宠物物品SQL语句[" + sql.toString() + "]");
        }

        int ret = this.update(sql.toString());
        return ret > 0;
    }

    public List<Object[]> searchPet(int petType, int levelMin, int levelMax, int priceMin, int priceMax, List<MarketSearchAttr> talents, List<MarketSearchAttr> attrs, int skillNumber, int score, int sellState) {
        StringBuilder sql = new StringBuilder("SELECT ID FROM ITEM_PET WHERE ");
        sql.append("ITEMID=").append(petType).append(" AND ");
        if (levelMin >= 0) {
            sql.append("LEVEL>=").append(levelMin).append(" AND ");
        }

        if (levelMin != levelMax && levelMax > 0) {
            sql.append("LEVEL<=").append(levelMax).append(" AND ");
        }

        if (priceMin > 0) {
            sql.append("PRICE>=").append(priceMin).append(" AND ");
        }

        if (priceMax > 0) {
            sql.append("PRICE<=").append(priceMax).append(" AND ");
        }

        if (talents != null && talents.size() > 0) {
            for(MarketSearchAttr marketSearchAttr : talents) {
                if (marketSearchAttr.attrid == 1440) {
                    sql.append("ATTACKAPT>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 1450) {
                    sql.append("DEFENDAPT>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 1460) {
                    sql.append("PHYFORCEAPT>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 1470) {
                    sql.append("MAGICAPT>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 1480) {
                    sql.append("SPEEDAPT>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 1500) {
                    sql.append("GROWRATE>=").append(marketSearchAttr.attrval).append(" AND ");
                }
            }
        }

        if (attrs != null && attrs.size() > 0) {
            for(MarketSearchAttr marketSearchAttr : attrs) {
                if (marketSearchAttr.attrid == 130) {
                    sql.append("ATTACK>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 140) {
                    sql.append("DEFEND>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 200) {
                    sql.append("SPEED>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 150) {
                    sql.append("MAGICATTACK>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 160) {
                    sql.append("MAGCIDEF>=").append(marketSearchAttr.attrval).append(" AND ");
                } else if (marketSearchAttr.attrid == 60) {
                    sql.append("MAXHP>=").append(marketSearchAttr.attrval).append(" AND ");
                }
            }
        }

        if (skillNumber > 0) {
            sql.append("SKILLNUMBER>=").append(skillNumber).append(" AND ");
        }

        if (score > 0) {
            sql.append("PETSCORE>=").append(score).append(" AND ");
        }

        long nowTime = System.currentTimeMillis();
        if (sellState == 1) {
            sql.append("SHOWTIME<=").append(nowTime).append(" AND ");
            sql.append("EXPIRETIME>").append(nowTime);
        } else if (sellState == 2) {
            sql.append("SHOWTIME>").append(nowTime);
        }

        if (MarketManager.LOG.isDebugEnabled()) {
            MarketManager.LOG.debug("宠物搜索=" + sql);
        }

        List<Object[]> results = null;

        try {
            results = (List)JdbcTemplate.getInstance().query(sql.toString(), new ArrayHandler());
        } catch (SQLException e) {
            MarketManager.LOG.error("market查询数据库item_pet表记录错误:", e);
        }

        return results;
    }

    public List<MarketGoods> getAll() {
        StringBuilder sql = new StringBuilder("SELECT * FROM ITEM_PET ");
        if (MarketManager.LOG.isDebugEnabled()) {
            MarketManager.LOG.debug("获取所有宠物物品SQL语句[" + sql.toString() + "]");
        }

        List<MarketGoods> results = null;

        try {
            results = (List)JdbcTemplate.getInstance().query(sql.toString(), new MarketGoodsHandler());
        } catch (SQLException e) {
            MarketManager.LOG.error("market获取数据库ITEM_PET表所有记录错误:", e);
        }

        return results;
    }

    /** @deprecated */
    public boolean removePet(long roleId, long uniqueId, int itemId, int key) {
        StringBuilder sql = new StringBuilder("DELETE FROM ITEM_PET WHERE ");
        sql.append("roleId=").append(roleId).append(" and ");
        sql.append("uniqueId=").append(uniqueId).append(" and ");
        sql.append("itemId=").append(itemId).append(" and ");
        sql.append("key=").append(key);
        if (MarketManager.LOG.isDebugEnabled()) {
            MarketManager.LOG.debug("删除拍卖宠物物品SQL语句[" + sql.toString() + "]");
        }

        int ret = this.update(sql.toString());
        return ret > 0;
    }

    private static class PetDAOHolder {
        static PetDAO instance = new PetDAO((PetDAO)null);
    }
}
