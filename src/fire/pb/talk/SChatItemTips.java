//
//

package fire.pb.talk;

import com.locojoy.base.Octets;
import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;

public class SChatItemTips extends __SChatItemTips__ {
    public static final int PROTOCOL_TYPE = 792446;
    public DisplayInfo displayinfo;
    public Octets tips;

    protected void process() {
    }

    public int getType() {
        return 792446;
    }

    public SChatItemTips() {
        this.displayinfo = new DisplayInfo();
        this.tips = new Octets();
    }

    public SChatItemTips(DisplayInfo _displayinfo_, Octets _tips_) {
        this.displayinfo = _displayinfo_;
        this.tips = _tips_;
    }

    public final boolean _validator_() {
        return this.displayinfo._validator_();
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.displayinfo);
            _os_.marshal(this.tips);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.displayinfo.unmarshal(_os_);
        this.tips = _os_.unmarshal_Octets();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof SChatItemTips) {
            SChatItemTips _o_ = (SChatItemTips)_o1_;
            if (!this.displayinfo.equals(_o_.displayinfo)) {
                return false;
            } else {
                return this.tips.equals(_o_.tips);
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.displayinfo.hashCode();
        _h_ += this.tips.hashCode();
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.displayinfo).append(",");
        _sb_.append("B").append(this.tips.size()).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }
}
