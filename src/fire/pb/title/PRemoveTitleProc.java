//
//

package fire.pb.title;

import mkdb.Procedure;

public class PRemoveTitleProc extends Procedure {
    private long roleid;
    private int titleid;

    public PRemoveTitleProc(long roleid, int titleid) {
        this.roleid = roleid;
        this.titleid = titleid;
    }

    public boolean process() {
        Title title = new Title(this.roleid, false);
        return title.removeTitle(this.titleid);
    }
}
