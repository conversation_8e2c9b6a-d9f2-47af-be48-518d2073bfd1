//
//

package fire.pb.scene;

import com.locojoy.base.Octets;
import fire.msp.move.MCreateDynamicScene;
import fire.pb.map.MapConfig;
import java.util.Map;

public final class SceneFactory {
    public static StaticScene createStaticScene(MapConfig cfg) {
        if (cfg == null) {
            return null;
        } else {
            StaticScene ss = null;
            if (cfg.visibletype == 0) {
                ss = new StaticScene();
            } else if (cfg.visibletype == 1) {
                ss = new SingleStaticScene();
            } else if (cfg.visibletype == 2) {
                ss = new TeamStaticScene();
            }

            if (ss == null) {
                Module.logger.error("静态场景初始化错误，MapID = " + cfg.id + ", name = " + cfg.mapName + "dynamic = " + cfg.dynamic);
                return null;
            } else {
                ss.setSceneID((long)cfg.id);
                ss.setName(cfg.mapName);
                ss.setDynamicType(cfg.dynamic);
                ss.load();
                ss.setMapConfig(cfg);
                SceneManager.getInstance().addNewStaticScene(ss);
                return ss;
            }
        }
    }

    public static DynamicTemplate createDynamicTemplate(MapConfig cfg) {
        DynamicTemplate dt = new DynamicTemplate();
        dt.setMapID(cfg.id);
        dt.setName(cfg.mapName);
        dt.setDynamic(cfg.dynamic);
        dt.setMapConfig(cfg);
        dt.load();
        SceneManager.getInstance().addNewTemplate(dt);
        return dt;
    }

    public static DynamicScene createDynamicScene(int mapID, long ownerID, String ownerName, int livetime, int scenetype, Octets parameters) {
        DynamicScene ds = null;
        ds = new DynamicScene();
        ds.setMapID(mapID);
        ds.setOwnerID(ownerID);
        ds.setScenetype(scenetype);
        ds.setOwnerName(ownerName);
        ds.setLiveTime(livetime);
        ds.setInitparams(parameters);
        ds.load();
        if (ds.getSceneID() == 0L) {
            return null;
        } else {
            ds.update(ds.getInitparams());
            SceneClient.pSend(new MCreateDynamicScene(ds.getSceneID(), ownerID, scenetype));
            return ds;
        }
    }
}
