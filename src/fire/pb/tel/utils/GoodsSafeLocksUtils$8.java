//
//

package fire.pb.tel.utils;

import fire.pb.SGoodUnLock;
import mkdb.Procedure;
import xbean.Properties;

final class GoodsSafeLocksUtils$8 extends Procedure {
    GoodsSafeLocksUtils$8(long var1, String var3, SGoodUnLock var4) {
        this.val$roleId = var1;
        this.val$password = var3;
        this.val$msg = var4;
    }

    protected boolean process() throws Exception {
        Properties prop = xtable.Properties.get(this.val$roleId);
        if (prop != null) {
            boolean isTrue = GoodsSafeLocksUtils.access$100(this.val$roleId, this.val$password, this.val$msg, prop);
            if (isTrue) {
                this.val$msg.status = 1;
                Procedure.psendWhileCommit(this.val$roleId, this.val$msg);
            }
        }

        return true;
    }
}
