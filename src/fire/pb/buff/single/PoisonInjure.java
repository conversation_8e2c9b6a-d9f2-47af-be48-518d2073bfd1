//
//

package fire.pb.buff.single;

import fire.script.JavaScript;

public class PoisonInjure extends Injure {
    public PoisonInjure(SingleBuffConfig buffConfig) {
        super(buffConfig);
    }

    protected void processInjure() {
        this.damage = (float)Math.min(-1, this.damageJS.eval(this.battleInfo.getEngine(), this.opfighter, this.aimfighter).intValue());
        this.damage = (float)((int)this.amendPoisonInjure((double)this.damage));
        this.demoresult.hpchange = (int)Math.min((float)(-(this.aimfighter.getEffectRole().getLevel() / 10 + 1)), Math.max((float)(-(this.aimfighter.getEffectRole().getLevel() * 16 + 20)), this.damage));
        this.handleAim();
    }

    protected double amendPoisonInjure(double poisonInjure) {
        return this.amendInjure(poisonInjure);
    }

    protected void handleMpChange() {
        if (this.effects.contains<PERSON>ey(101)) {
            this.demoresult.mpchange = ((JavaScript)this.effects.get(101)).eval(this.battleInfo.getEngine(), this.opfighter, this.aimfighter).intValue();
            this.demoresult.mpchange = (int)this.randomValueInScale((double)this.demoresult.mpchange, 0.95F, 1.05F);
            if (this.demoresult.mpchange > -1) {
                this.demoresult.mpchange = -1;
            }
        } else if (this.effects.containsKey(102)) {
            this.demoresult.mpchange = (int)(((JavaScript)this.effects.get(102)).eval(this.battleInfo.getEngine(), this.opfighter, this.aimfighter) * (double)this.aimfighter.getEffectRole().getMp());
            if (this.demoresult.mpchange > -1) {
                this.demoresult.mpchange = -1;
            }
        }

        if (this.demoresult.mpchange != 0) {
            this.demoresult.mpchange = Math.min(-(this.aimfighter.getEffectRole().getLevel() / 10 + 1), Math.max(-(this.aimfighter.getEffectRole().getLevel() * 16 + 20), this.demoresult.mpchange));
            this.aimfighter.attachMpChange(this.demoresult.mpchange);
        }

    }

    protected void handleAttackCount() {
    }
}
