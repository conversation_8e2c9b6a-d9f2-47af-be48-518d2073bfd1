//
//

package fire.pb.school;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Octets;

public class SSendShouxiInfo extends __SSendShouxiInfo__ {
    public static final int PROTOCOL_TYPE = 810433;
    public ShouxiInfo shouxi;
    public long shouxikey;

    protected void process() {
    }

    public int getType() {
        return 810433;
    }

    public SSendShouxiInfo() {
        this.shouxi = new ShouxiInfo();
    }

    public SSendShouxiInfo(ShouxiInfo _shouxi_, long _shouxikey_) {
        this.shouxi = _shouxi_;
        this.shouxikey = _shouxikey_;
    }

    public final boolean _validator_() {
        return this.shouxi._validator_();
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.shouxi);
            _os_.marshal(this.shouxikey);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.shouxi.unmarshal(_os_);
        this.shouxikey = _os_.unmarshal_long();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof SSendShouxiInfo) {
            SSendShouxiInfo _o_ = (SSendShouxiInfo)_o1_;
            if (!this.shouxi.equals(_o_.shouxi)) {
                return false;
            } else {
                return this.shouxikey == _o_.shouxikey;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += this.shouxi.hashCode();
        _h_ += (int)this.shouxikey;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.shouxi).append(",");
        _sb_.append(this.shouxikey).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(SSendShouxiInfo _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = this.shouxi.compareTo(_o_.shouxi);
            if (0 != _c_) {
                return _c_;
            } else {
                _c_ = Long.signum(this.shouxikey - _o_.shouxikey);
                return 0 != _c_ ? _c_ : _c_;
            }
        }
    }
}
