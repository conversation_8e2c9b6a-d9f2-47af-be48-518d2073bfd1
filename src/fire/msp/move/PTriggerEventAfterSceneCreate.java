//
//

package fire.msp.move;

import fire.pb.activity.DelayActivityManager;
import mkdb.Procedure;

public class PTriggerEventAfterSceneCreate extends Procedure {
    private long sceneid;
    private long ownerid;
    private int scenetype;

    public PTriggerEventAfterSceneCreate(long sceneid, long ownerid, int scenetype) {
        this.sceneid = sceneid;
        this.ownerid = ownerid;
        this.scenetype = scenetype;
    }

    protected boolean process() throws Exception {
        if (this.scenetype == 0) {
            return false;
        } else {
            DelayActivityManager.getInstance().executeDelayActivity(this.sceneid, this.ownerid, this.scenetype);
            return true;
        }
    }
}
