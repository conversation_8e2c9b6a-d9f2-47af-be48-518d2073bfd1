//
//

package fire.msp.role;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import fire.pb.scene.manager.RoleManager;
import fire.pb.scene.movable.Role;
import com.locojoy.base.Octets;
import java.util.Set;

public class GSetPlayCGState extends __GSetPlayCGState__ {
    public static final int PROTOCOL_TYPE = 730899;
    public long roleid;
    public byte isplaying;

    protected void process() {
        Role role = RoleManager.getInstance().getRoleByID(this.roleid);
        if (role != null) {
            role.setPlayingCGState(this.isplaying == 1);
        }

    }

    public int getType() {
        return 730899;
    }

    public GSetPlayCGState() {
    }

    public GSetPlayCGState(long _roleid_, byte _isplaying_) {
        this.roleid = _roleid_;
        this.isplaying = _isplaying_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.roleid);
            _os_.marshal(this.isplaying);
            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.roleid = _os_.unmarshal_long();
        this.isplaying = _os_.unmarshal_byte();
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof GSetPlayCGState) {
            GSetPlayCGState _o_ = (GSetPlayCGState)_o1_;
            if (this.roleid != _o_.roleid) {
                return false;
            } else {
                return this.isplaying == _o_.isplaying;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += (int)this.roleid;
        _h_ += this.isplaying;
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.roleid).append(",");
        _sb_.append(this.isplaying).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }

    public int compareTo(GSetPlayCGState _o_) {
        if (_o_ == this) {
            return 0;
        } else {
            int _c_ = 0;
            _c_ = Long.signum(this.roleid - _o_.roleid);
            if (0 != _c_) {
                return _c_;
            } else {
                _c_ = this.isplaying - _o_.isplaying;
                return 0 != _c_ ? _c_ : _c_;
            }
        }
    }
}
