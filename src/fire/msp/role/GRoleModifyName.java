//
//

package fire.msp.role;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import fire.pb.SModifyRoleName;
import fire.pb.scene.manager.RoleManager;
import fire.pb.scene.movable.Role;
import gnet.link.Onlines;
import java.util.HashSet;
import mkio.Protocol;
import com.locojoy.base.Octets;
import java.util.Set;

public class GRoleModifyName extends __GRoleModifyName__ {
    public static final int PROTOCOL_TYPE = 730917;
    public long roleid;
    public String newname;
    public HashSet<Long> teammembers;

    protected void process() {
        Role role = RoleManager.getInstance().getRoleByID(this.roleid);
        if (role != null) {
            if (null != role.getScene()) {
                role.setName(this.newname);
                role.marshal();
                Protocol send = new SModifyRoleName();
                ((SModifyRoleName)send).roleid = this.roleid;
                ((SModifyRoleName)send).newname = this.newname;
                if (role.checkVisible()) {
                    role.sendAround(send);
                }

                if (!this.teammembers.isEmpty()) {
                    Onlines.getInstance().send(this.teammembers, send);
                }

            }
        }
    }

    public int getType() {
        return 730917;
    }

    public GRoleModifyName() {
        this.newname = "";
        this.teammembers = new HashSet();
    }

    public GRoleModifyName(long _roleid_, String _newname_, HashSet<Long> _teammembers_) {
        this.roleid = _roleid_;
        this.newname = _newname_;
        this.teammembers = _teammembers_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.roleid);
            _os_.marshal(this.newname, "UTF-16LE");
            _os_.compact_uint32(this.teammembers.size());

            for(Long _v_ : this.teammembers) {
                _os_.marshal(_v_);
            }

            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.roleid = _os_.unmarshal_long();
        this.newname = _os_.unmarshal_String("UTF-16LE");

        for(int _size_ = _os_.uncompact_uint32(); _size_ > 0; --_size_) {
            long _v_ = _os_.unmarshal_long();
            this.teammembers.add(_v_);
        }

        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof GRoleModifyName) {
            GRoleModifyName _o_ = (GRoleModifyName)_o1_;
            if (this.roleid != _o_.roleid) {
                return false;
            } else if (!this.newname.equals(_o_.newname)) {
                return false;
            } else {
                return this.teammembers.equals(_o_.teammembers);
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += (int)this.roleid;
        _h_ += this.newname.hashCode();
        _h_ += this.teammembers.hashCode();
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.roleid).append(",");
        _sb_.append("T").append(this.newname.length()).append(",");
        _sb_.append(this.teammembers).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }
}
