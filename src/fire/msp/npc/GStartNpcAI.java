//
//

package fire.msp.npc;

import com.locojoy.base.Marshal.MarshalException;
import com.locojoy.base.Marshal.OctetsStream;
import fire.pb.scene.npcai.AIManager;
import java.util.HashSet;
import com.locojoy.base.Octets;
import java.util.Set;

public class GStartNpcAI extends __GStartNpcAI__ {
    public static final int PROTOCOL_TYPE = 730433;
    public long npckey;
    public int aiid;
    public HashSet<Long> roleids;

    protected void process() {
        AIManager.getInstance().npcMoveByAiID(this.npckey, this.aiid, this.roleids);
    }

    public int getType() {
        return 730433;
    }

    public GStartNpcAI() {
        this.roleids = new HashSet();
    }

    public GStartNpcAI(long _npckey_, int _aiid_, HashSet<Long> _roleids_) {
        this.npckey = _npckey_;
        this.aiid = _aiid_;
        this.roleids = _roleids_;
    }

    public final boolean _validator_() {
        return true;
    }

    public OctetsStream marshal(OctetsStream _os_) {
        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            _os_.marshal(this.npckey);
            _os_.marshal(this.aiid);
            _os_.compact_uint32(this.roleids.size());

            for(Long _v_ : this.roleids) {
                _os_.marshal(_v_);
            }

            return _os_;
        }
    }

    public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
        this.npckey = _os_.unmarshal_long();
        this.aiid = _os_.unmarshal_int();

        for(int _size_ = _os_.uncompact_uint32(); _size_ > 0; --_size_) {
            long _v_ = _os_.unmarshal_long();
            this.roleids.add(_v_);
        }

        if (!this._validator_()) {
            throw new VerifyError("validator failed");
        } else {
            return _os_;
        }
    }

    public boolean equals(Object _o1_) {
        if (_o1_ == this) {
            return true;
        } else if (_o1_ instanceof GStartNpcAI) {
            GStartNpcAI _o_ = (GStartNpcAI)_o1_;
            if (this.npckey != _o_.npckey) {
                return false;
            } else if (this.aiid != _o_.aiid) {
                return false;
            } else {
                return this.roleids.equals(_o_.roleids);
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int _h_ = 0;
        _h_ += (int)this.npckey;
        _h_ += this.aiid;
        _h_ += this.roleids.hashCode();
        return _h_;
    }

    public String toString() {
        StringBuilder _sb_ = new StringBuilder();
        _sb_.append("(");
        _sb_.append(this.npckey).append(",");
        _sb_.append(this.aiid).append(",");
        _sb_.append(this.roleids).append(",");
        _sb_.append(")");
        return _sb_.toString();
    }
}
